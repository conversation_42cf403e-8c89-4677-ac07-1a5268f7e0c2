<?php
require_once __DIR__ . '/../config/config.php';

// Initialize cart for count
$cart = new GeusGalore\Cart();
$cartCount = $cart->getCartItemCount(current_user_id());
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME . ' - Papua New Guinea\'s Premier Online Store'; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Shop the best products in Papua New Guinea at Geu\'s Galore. Quality products, great prices, and excellent service.'; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css?v=<?php echo time(); ?>">
    <!-- Fallback CSS path -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Epic Header -->
    <header class="header">
        <div class="header-main">
            <div class="container">
                <div class="header-content">
                    <!-- Logo Section -->
                    <div class="logo-section">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-crown"></i>
                            <?php echo SITE_NAME; ?>
                        </a>
                    </div>

                    <!-- Main Navigation -->
                    <nav class="main-nav">
                        <ul class="nav-menu">
                            <li><a href="<?php echo SITE_URL; ?>"><i class="fas fa-home"></i> Home</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/shop/products.php"><i class="fas fa-shopping-bag"></i> Shop</a></li>
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle">
                                    <i class="fas fa-th-large"></i> Categories
                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <?php
                                    // Get categories for menu
                                    $db = GeusGalore\Database::getInstance();
                                    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name LIMIT 8");
                                    if (!empty($categories)):
                                        foreach ($categories as $category):
                                    ?>
                                        <li>
                                            <a href="<?php echo SITE_URL; ?>/shop/products.php?category=<?php echo $category['id']; ?>">
                                                <i class="fas fa-tag"></i>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </a>
                                        </li>
                                    <?php
                                        endforeach;
                                    else:
                                    ?>
                                        <li><a href="<?php echo SITE_URL; ?>/shop/products.php"><i class="fas fa-box"></i> All Products</a></li>
                                    <?php endif; ?>
                                    <li class="dropdown-divider"></li>
                                    <li><a href="<?php echo SITE_URL; ?>/shop/products.php"><i class="fas fa-eye"></i> View All Categories</a></li>
                                </ul>
                            </li>
                            <li><a href="<?php echo SITE_URL; ?>/quote/request.php"><i class="fas fa-file-invoice-dollar"></i> Get Quote</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/pages/about.php"><i class="fas fa-info-circle"></i> About</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/contact.php"><i class="fas fa-envelope"></i> Contact</a></li>
                        </ul>
                    </nav>

                    <!-- Right Section -->
                    <div class="header-right">
                        <!-- Search Bar -->
                        <form class="search-bar" action="<?php echo SITE_URL; ?>/shop/products.php" method="GET">
                            <input type="text" name="search" class="search-input" placeholder="Search products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>

                        <!-- Auth & Cart Section -->
                        <div class="auth-cart-section">
                            <!-- Auth Links -->
                            <?php if (is_logged_in()): ?>
                                <div class="dropdown user-dropdown">
                                    <a href="#" class="dropdown-toggle user-toggle">
                                        <i class="fas fa-user-circle"></i>
                                        <span class="user-name">Account</span>
                                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                                    </a>
                                    <ul class="dropdown-menu user-menu">
                                        <li><a href="<?php echo SITE_URL; ?>/user/dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                        <li><a href="<?php echo SITE_URL; ?>/user/orders.php"><i class="fas fa-shopping-bag"></i> My Orders</a></li>
                                        <li><a href="<?php echo SITE_URL; ?>/user/profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                                        <li class="dropdown-divider"></li>
                                        <li><a href="<?php echo SITE_URL; ?>/auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <div class="auth-links">
                                    <a href="<?php echo SITE_URL; ?>/auth/login.php" class="auth-link login-link">
                                        <i class="fas fa-sign-in-alt"></i> Login
                                    </a>
                                    <a href="<?php echo SITE_URL; ?>/auth/register.php" class="auth-link register-link">
                                        <i class="fas fa-user-plus"></i> Register
                                    </a>
                                </div>
                            <?php endif; ?>

                            <!-- Cart Icon -->
                            <a href="<?php echo SITE_URL; ?>/shop/cart.php" class="cart-icon">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="cart-count" style="<?php echo $cartCount > 0 ? '' : 'display: none;'; ?>">
                                    <?php echo $cartCount; ?>
                                </span>
                            </a>
                        </div>

                        <!-- Mobile Menu Toggle -->
                        <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" onclick="closeMobileMenu()"></div>

    <!-- Mobile Menu -->
    <div class="mobile-menu">
        <div class="mobile-menu-header">
            <div class="logo" style="font-size: 20px; padding: 0; background: none; border: none; box-shadow: none;">
                <i class="fas fa-crown"></i>
                <?php echo SITE_NAME; ?>
            </div>
            <div class="mobile-menu-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </div>
        </div>
        <div class="mobile-menu-content">
            <ul class="mobile-nav-menu">
                <li><a href="<?php echo SITE_URL; ?>"><i class="fas fa-home"></i> Home</a></li>
                <li><a href="<?php echo SITE_URL; ?>/shop/products.php"><i class="fas fa-shopping-bag"></i> Shop</a></li>
                <li><a href="<?php echo SITE_URL; ?>/quote/request.php"><i class="fas fa-file-invoice-dollar"></i> Get Quote</a></li>
                <li><a href="<?php echo SITE_URL; ?>/pages/about.php"><i class="fas fa-info-circle"></i> About</a></li>
                <li><a href="<?php echo SITE_URL; ?>/contact.php"><i class="fas fa-envelope"></i> Contact</a></li>

                <!-- Categories in Mobile -->
                <?php if (!empty($categories)): ?>
                    <li style="margin-top: 20px; padding: 10px 15px; color: rgba(255,255,255,0.8); font-size: 12px; text-transform: uppercase; letter-spacing: 1px; font-weight: 700;">Categories</li>
                    <?php foreach ($categories as $category): ?>
                        <li>
                            <a href="<?php echo SITE_URL; ?>/shop/products.php?category=<?php echo $category['id']; ?>">
                                <i class="fas fa-tag"></i>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                <?php endif; ?>

                <!-- Auth Links in Mobile -->
                <li style="margin-top: 20px; padding: 10px 15px; color: rgba(255,255,255,0.8); font-size: 12px; text-transform: uppercase; letter-spacing: 1px; font-weight: 700;">Account</li>
                <?php if (is_logged_in()): ?>
                    <li><a href="<?php echo SITE_URL; ?>/user/dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/user/orders.php"><i class="fas fa-shopping-bag"></i> My Orders</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/user/profile.php"><i class="fas fa-user-edit"></i> Profile</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                <?php else: ?>
                    <li><a href="<?php echo SITE_URL; ?>/auth/login.php"><i class="fas fa-sign-in-alt"></i> Login</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/auth/register.php"><i class="fas fa-user-plus"></i> Register</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

    <script>
    function toggleMobileMenu() {
        const overlay = document.querySelector('.mobile-menu-overlay');
        const menu = document.querySelector('.mobile-menu');

        overlay.classList.toggle('active');
        menu.classList.toggle('active');
        document.body.style.overflow = menu.classList.contains('active') ? 'hidden' : '';
    }

    function closeMobileMenu() {
        const overlay = document.querySelector('.mobile-menu-overlay');
        const menu = document.querySelector('.mobile-menu');

        overlay.classList.remove('active');
        menu.classList.remove('active');
        document.body.style.overflow = '';
    }

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.mobile-nav-menu a').forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });
    </script>
    
    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash'])): ?>
        <div class="flash-messages">
            <?php foreach ($_SESSION['flash'] as $type => $message): ?>
                <div class="flash-message flash-<?php echo $type; ?>" style="
                    position: fixed;
                    top: 100px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 5px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    <?php
                    switch ($type) {
                        case 'success':
                            echo 'background-color: #28a745;';
                            break;
                        case 'error':
                            echo 'background-color: #dc3545;';
                            break;
                        case 'warning':
                            echo 'background-color: #ffc107; color: #000;';
                            break;
                        default:
                            echo 'background-color: #17a2b8;';
                    }
                    ?>
                ">
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <?php unset($_SESSION['flash'][$type]); ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="main-content">
