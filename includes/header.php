<?php
require_once __DIR__ . '/../config/config.php';

// Initialize cart for count
$cart = new GeusGalore\Cart();
$cartCount = $cart->getCartItemCount(current_user_id());
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME . ' - Papua New Guinea\'s Premier Online Store'; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Shop the best products in Papua New Guinea at Geu\'s Galore. Quality products, great prices, and excellent service.'; ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Epic Header & Navigation - Complete Built-in CSS */

        /* Reset and Base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        /* Container */
        .epic-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Epic Header */
        .epic-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(20px);
        }

        .epic-header-main {
            padding: 15px 0;
        }

        .epic-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        /* Epic Logo */
        .epic-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            color: white;
            font-size: 24px;
            font-weight: 900;
            transition: all 0.3s ease;
        }

        .epic-logo:hover {
            transform: scale(1.05);
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }

        .epic-logo i {
            font-size: 28px;
            background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Epic Navigation */
        .epic-nav {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .epic-nav-menu {
            display: flex;
            list-style: none;
            gap: 25px;
            margin: 0;
            padding: 0;
        }

        .epic-nav-item {
            position: relative;
        }

        .epic-nav-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            padding: 12px 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .epic-nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transition: left 0.3s ease;
        }

        .epic-nav-link:hover::before {
            left: 0;
        }

        .epic-nav-link:hover {
            background: rgba(255,255,255,0.15);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Epic Dropdown */
        .epic-dropdown {
            position: relative;
        }

        .epic-dropdown-toggle {
            cursor: pointer;
        }

        .epic-dropdown-arrow {
            transition: transform 0.3s ease;
            margin-left: 5px;
        }

        .epic-dropdown:hover .epic-dropdown-arrow {
            transform: rotate(180deg);
        }

        .epic-dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            min-width: 250px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            list-style: none;
            padding: 15px 0;
            margin: 10px 0 0 0;
        }

        .epic-dropdown:hover .epic-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .epic-dropdown-item {
            margin: 0;
        }

        .epic-dropdown-link {
            display: flex;
            align-items: center;
            gap: 12px;
            color: #2c3e50;
            text-decoration: none;
            padding: 12px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .epic-dropdown-link:hover {
            background: rgba(102,126,234,0.1);
            color: #667eea;
            transform: translateX(5px);
        }

        .epic-dropdown-divider {
            height: 1px;
            background: rgba(0,0,0,0.1);
            margin: 10px 20px;
        }

        /* Epic Right Section */
        .epic-header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        /* Epic Search */
        .epic-search {
            position: relative;
            display: flex;
            align-items: center;
        }

        .epic-search-input {
            width: 300px;
            padding: 12px 50px 12px 20px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .epic-search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .epic-search-input:focus {
            outline: none;
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
            box-shadow: 0 0 0 3px rgba(255,255,255,0.1);
            transform: scale(1.02);
        }

        .epic-search-btn {
            position: absolute;
            right: 5px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .epic-search-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        /* Epic Auth & Cart */
        .epic-auth-cart {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .epic-auth-links {
            display: flex;
            gap: 10px;
        }

        .epic-auth-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .epic-login-link {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .epic-login-link:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
            transform: translateY(-2px);
        }

        .epic-register-link {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            border: 2px solid transparent;
        }

        .epic-register-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255,107,107,0.4);
        }

        /* Epic Cart */
        .epic-cart {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            color: white;
            text-decoration: none;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .epic-cart:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
            transform: translateY(-2px);
            color: white;
        }

        .epic-cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            font-size: 12px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 12px;
            min-width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Epic Mobile Menu Toggle */
        .epic-mobile-toggle {
            display: none;
            flex-direction: column;
            gap: 4px;
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .epic-mobile-toggle:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .epic-mobile-toggle span {
            width: 25px;
            height: 3px;
            background: white;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .epic-mobile-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .epic-mobile-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .epic-mobile-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        /* Epic Mobile Menu */
        .epic-mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .epic-mobile-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .epic-mobile-menu {
            position: fixed;
            top: 0;
            right: -100%;
            width: 320px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 9999;
            transition: right 0.3s ease;
            overflow-y: auto;
        }

        .epic-mobile-menu.active {
            right: 0;
        }

        .epic-mobile-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border-bottom: 2px solid rgba(255,255,255,0.1);
        }

        .epic-mobile-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-size: 20px;
            font-weight: 900;
        }

        .epic-mobile-logo i {
            font-size: 24px;
            background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .epic-mobile-close {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .epic-mobile-close:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .epic-mobile-content {
            padding: 20px 0;
        }

        .epic-mobile-nav {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .epic-mobile-nav-item {
            margin: 0;
        }

        .epic-mobile-nav-link {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .epic-mobile-nav-link:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #ffd93d;
            transform: translateX(5px);
        }

        .epic-mobile-section-title {
            margin-top: 30px;
            padding: 10px 20px;
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        /* Epic Mobile Search */
        .epic-mobile-search {
            margin: 20px;
            position: relative;
        }

        .epic-mobile-search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            color: white;
            font-size: 16px;
        }

        .epic-mobile-search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .epic-mobile-search-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .epic-search-input {
                width: 250px;
            }
        }

        @media (max-width: 992px) {
            .epic-nav {
                display: none;
            }

            .epic-mobile-toggle {
                display: flex;
            }

            .epic-search {
                display: none;
            }

            .epic-auth-links {
                display: none;
            }

            .epic-dropdown {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .epic-header-main {
                padding: 12px 0;
            }

            .epic-container {
                padding: 0 15px;
            }

            .epic-logo {
                font-size: 20px;
            }

            .epic-logo i {
                font-size: 24px;
            }

            .epic-cart {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .epic-mobile-menu {
                width: 280px;
            }
        }

        @media (max-width: 480px) {
            .epic-mobile-menu {
                width: 100%;
            }
        }
    </style>

    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Epic Header -->
    <header class="epic-header">
        <div class="epic-header-main">
            <div class="epic-container">
                <div class="epic-header-content">
                    <!-- Epic Logo -->
                    <a href="<?php echo SITE_URL; ?>" class="epic-logo">
                        <i class="fas fa-crown"></i>
                        <?php echo SITE_NAME; ?>
                    </a>

                    <!-- Epic Desktop Navigation -->
                    <nav class="epic-nav">
                        <ul class="epic-nav-menu">
                            <li class="epic-nav-item">
                                <a href="<?php echo SITE_URL; ?>" class="epic-nav-link">
                                    <i class="fas fa-home"></i> Home
                                </a>
                            </li>
                            <li class="epic-nav-item">
                                <a href="<?php echo SITE_URL; ?>/shop/products.php" class="epic-nav-link">
                                    <i class="fas fa-shopping-bag"></i> Shop
                                </a>
                            </li>
                            <li class="epic-nav-item epic-dropdown">
                                <a href="#" class="epic-nav-link epic-dropdown-toggle">
                                    <i class="fas fa-th-large"></i> Categories
                                    <i class="fas fa-chevron-down epic-dropdown-arrow"></i>
                                </a>
                                <ul class="epic-dropdown-menu">
                                    <?php
                                    // Get categories for menu
                                    $db = GeusGalore\Database::getInstance();
                                    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name LIMIT 8");
                                    if (!empty($categories)):
                                        foreach ($categories as $category):
                                    ?>
                                        <li class="epic-dropdown-item">
                                            <a href="<?php echo SITE_URL; ?>/shop/products.php?category=<?php echo $category['id']; ?>" class="epic-dropdown-link">
                                                <i class="fas fa-tag"></i>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </a>
                                        </li>
                                    <?php
                                        endforeach;
                                    else:
                                    ?>
                                        <li class="epic-dropdown-item">
                                            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="epic-dropdown-link">
                                                <i class="fas fa-box"></i> All Products
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    <div class="epic-dropdown-divider"></div>
                                    <li class="epic-dropdown-item">
                                        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="epic-dropdown-link">
                                            <i class="fas fa-eye"></i> View All Categories
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="epic-nav-item">
                                <a href="<?php echo SITE_URL; ?>/quote/request.php" class="epic-nav-link">
                                    <i class="fas fa-file-invoice-dollar"></i> Get Quote
                                </a>
                            </li>
                            <li class="epic-nav-item">
                                <a href="<?php echo SITE_URL; ?>/pages/about.php" class="epic-nav-link">
                                    <i class="fas fa-info-circle"></i> About
                                </a>
                            </li>
                            <li class="epic-nav-item">
                                <a href="<?php echo SITE_URL; ?>/contact.php" class="epic-nav-link">
                                    <i class="fas fa-envelope"></i> Contact
                                </a>
                            </li>
                        </ul>
                    </nav>

                    <!-- Epic Right Section -->
                    <div class="epic-header-right">
                        <!-- Epic Search -->
                        <form class="epic-search" action="<?php echo SITE_URL; ?>/shop/products.php" method="GET">
                            <input type="text" name="search" class="epic-search-input" placeholder="Search products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button type="submit" class="epic-search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>

                        <!-- Epic Auth & Cart -->
                        <div class="epic-auth-cart">
                            <!-- Epic Auth Links -->
                            <?php if (is_logged_in()): ?>
                                <div class="epic-dropdown">
                                    <a href="#" class="epic-nav-link epic-dropdown-toggle">
                                        <i class="fas fa-user-circle"></i> Account
                                        <i class="fas fa-chevron-down epic-dropdown-arrow"></i>
                                    </a>
                                    <ul class="epic-dropdown-menu">
                                        <li class="epic-dropdown-item">
                                            <a href="<?php echo SITE_URL; ?>/user/dashboard.php" class="epic-dropdown-link">
                                                <i class="fas fa-tachometer-alt"></i> Dashboard
                                            </a>
                                        </li>
                                        <li class="epic-dropdown-item">
                                            <a href="<?php echo SITE_URL; ?>/user/orders.php" class="epic-dropdown-link">
                                                <i class="fas fa-shopping-bag"></i> My Orders
                                            </a>
                                        </li>
                                        <li class="epic-dropdown-item">
                                            <a href="<?php echo SITE_URL; ?>/user/profile.php" class="epic-dropdown-link">
                                                <i class="fas fa-user-edit"></i> Profile
                                            </a>
                                        </li>
                                        <div class="epic-dropdown-divider"></div>
                                        <li class="epic-dropdown-item">
                                            <a href="<?php echo SITE_URL; ?>/auth/logout.php" class="epic-dropdown-link">
                                                <i class="fas fa-sign-out-alt"></i> Logout
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <div class="epic-auth-links">
                                    <a href="<?php echo SITE_URL; ?>/auth/login.php" class="epic-auth-link epic-login-link">
                                        <i class="fas fa-sign-in-alt"></i> Login
                                    </a>
                                    <a href="<?php echo SITE_URL; ?>/auth/register.php" class="epic-auth-link epic-register-link">
                                        <i class="fas fa-user-plus"></i> Register
                                    </a>
                                </div>
                            <?php endif; ?>

                            <!-- Epic Cart -->
                            <a href="<?php echo SITE_URL; ?>/shop/cart.php" class="epic-cart">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="epic-cart-count cart-count" style="<?php echo $cartCount > 0 ? '' : 'display: none;'; ?>">
                                    <?php echo $cartCount; ?>
                                </span>
                            </a>
                        </div>

                        <!-- Epic Mobile Toggle -->
                        <div class="epic-mobile-toggle" onclick="toggleEpicMobileMenu()">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Epic Mobile Menu Overlay -->
    <div class="epic-mobile-overlay" onclick="closeEpicMobileMenu()"></div>

    <!-- Epic Mobile Menu -->
    <div class="epic-mobile-menu">
        <div class="epic-mobile-header">
            <div class="epic-mobile-logo">
                <i class="fas fa-crown"></i>
                <?php echo SITE_NAME; ?>
            </div>
            <div class="epic-mobile-close" onclick="closeEpicMobileMenu()">
                <i class="fas fa-times"></i>
            </div>
        </div>

        <!-- Epic Mobile Search -->
        <form class="epic-mobile-search" action="<?php echo SITE_URL; ?>/shop/products.php" method="GET">
            <input type="text" name="search" class="epic-mobile-search-input" placeholder="Search products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
            <button type="submit" class="epic-mobile-search-btn">
                <i class="fas fa-search"></i>
            </button>
        </form>

        <div class="epic-mobile-content">
            <ul class="epic-mobile-nav">
                <li class="epic-mobile-nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="epic-mobile-nav-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="epic-mobile-nav-item">
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" class="epic-mobile-nav-link">
                        <i class="fas fa-shopping-bag"></i> Shop
                    </a>
                </li>
                <li class="epic-mobile-nav-item">
                    <a href="<?php echo SITE_URL; ?>/quote/request.php" class="epic-mobile-nav-link">
                        <i class="fas fa-file-invoice-dollar"></i> Get Quote
                    </a>
                </li>
                <li class="epic-mobile-nav-item">
                    <a href="<?php echo SITE_URL; ?>/pages/about.php" class="epic-mobile-nav-link">
                        <i class="fas fa-info-circle"></i> About
                    </a>
                </li>
                <li class="epic-mobile-nav-item">
                    <a href="<?php echo SITE_URL; ?>/contact.php" class="epic-mobile-nav-link">
                        <i class="fas fa-envelope"></i> Contact
                    </a>
                </li>

                <!-- Epic Categories in Mobile -->
                <?php if (!empty($categories)): ?>
                    <div class="epic-mobile-section-title">Categories</div>
                    <?php foreach ($categories as $category): ?>
                        <li class="epic-mobile-nav-item">
                            <a href="<?php echo SITE_URL; ?>/shop/products.php?category=<?php echo $category['id']; ?>" class="epic-mobile-nav-link">
                                <i class="fas fa-tag"></i>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                <?php endif; ?>

                <!-- Epic Auth Links in Mobile -->
                <div class="epic-mobile-section-title">Account</div>
                <?php if (is_logged_in()): ?>
                    <li class="epic-mobile-nav-item">
                        <a href="<?php echo SITE_URL; ?>/user/dashboard.php" class="epic-mobile-nav-link">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="epic-mobile-nav-item">
                        <a href="<?php echo SITE_URL; ?>/user/orders.php" class="epic-mobile-nav-link">
                            <i class="fas fa-shopping-bag"></i> My Orders
                        </a>
                    </li>
                    <li class="epic-mobile-nav-item">
                        <a href="<?php echo SITE_URL; ?>/user/profile.php" class="epic-mobile-nav-link">
                            <i class="fas fa-user-edit"></i> Profile
                        </a>
                    </li>
                    <li class="epic-mobile-nav-item">
                        <a href="<?php echo SITE_URL; ?>/auth/logout.php" class="epic-mobile-nav-link">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                <?php else: ?>
                    <li class="epic-mobile-nav-item">
                        <a href="<?php echo SITE_URL; ?>/auth/login.php" class="epic-mobile-nav-link">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                    </li>
                    <li class="epic-mobile-nav-item">
                        <a href="<?php echo SITE_URL; ?>/auth/register.php" class="epic-mobile-nav-link">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

    <script>
    // Epic Mobile Menu Functions
    function toggleEpicMobileMenu() {
        const overlay = document.querySelector('.epic-mobile-overlay');
        const menu = document.querySelector('.epic-mobile-menu');
        const toggle = document.querySelector('.epic-mobile-toggle');

        overlay.classList.toggle('active');
        menu.classList.toggle('active');
        toggle.classList.toggle('active');
        document.body.style.overflow = menu.classList.contains('active') ? 'hidden' : '';
    }

    function closeEpicMobileMenu() {
        const overlay = document.querySelector('.epic-mobile-overlay');
        const menu = document.querySelector('.epic-mobile-menu');
        const toggle = document.querySelector('.epic-mobile-toggle');

        overlay.classList.remove('active');
        menu.classList.remove('active');
        toggle.classList.remove('active');
        document.body.style.overflow = '';
    }

    // Close mobile menu when clicking on a link
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.epic-mobile-nav-link').forEach(link => {
            link.addEventListener('click', closeEpicMobileMenu);
        });
    });
    </script>
    
    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash'])): ?>
        <div class="flash-messages">
            <?php foreach ($_SESSION['flash'] as $type => $message): ?>
                <div class="flash-message flash-<?php echo $type; ?>" style="
                    position: fixed;
                    top: 100px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 5px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    <?php
                    switch ($type) {
                        case 'success':
                            echo 'background-color: #28a745;';
                            break;
                        case 'error':
                            echo 'background-color: #dc3545;';
                            break;
                        case 'warning':
                            echo 'background-color: #ffc107; color: #000;';
                            break;
                        default:
                            echo 'background-color: #17a2b8;';
                    }
                    ?>
                ">
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <?php unset($_SESSION['flash'][$type]); ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="main-content">
