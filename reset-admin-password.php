<?php
require_once 'config/config.php';

echo "<h2>🔐 Admin Password Reset Tool</h2>";
echo "<style>body{font-family:Arial;padding:20px;background:#f5f5f5;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    $db = GeusGalore\Database::getInstance();
    
    // Reset admin password to 'admin123'
    $newPassword = 'admin123';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    echo "<h3>Resetting Admin Password...</h3>";
    
    // Update the admin user password
    $updateResult = $db->update('admin_users', 
        ['password' => $hashedPassword], 
        'username = ? OR email = ?', 
        ['admin', '<EMAIL>']
    );
    
    if ($updateResult) {
        echo "<div class='success'>✅ Admin password reset successfully!</div>";
        echo "<div class='info'><strong>Login Details:</strong></div>";
        echo "<div class='info'>🌐 URL: <a href='admin/login.php'>admin/login.php</a></div>";
        echo "<div class='info'>👤 Username: <strong>admin</strong> (or <EMAIL>)</div>";
        echo "<div class='info'>🔑 Password: <strong>admin123</strong></div>";
        
        // Test the new password
        echo "<h3>Testing New Password...</h3>";
        $testAdmin = $db->fetch("SELECT * FROM admin_users WHERE username = 'admin'");
        
        if ($testAdmin && password_verify($newPassword, $testAdmin['password'])) {
            echo "<div class='success'>✅ Password verification test passed!</div>";
            echo "<div class='success'>🎉 You can now login to the admin panel!</div>";
        } else {
            echo "<div class='error'>❌ Password verification test failed</div>";
        }
        
    } else {
        echo "<div class='error'>❌ Failed to reset password</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<p><a href='admin/login.php' style='background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🔐 Try Admin Login Now</a></p>";
echo "<p><a href='index.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🏠 Back to Homepage</a></p>";
echo "<p style='color:#666;font-size:12px;'>⚠️ Delete this file (reset-admin-password.php) after use for security!</p>";
?>
