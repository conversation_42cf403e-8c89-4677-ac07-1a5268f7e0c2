<?php
require_once 'config/config.php';

$pageTitle = 'Checkout';
$pageDescription = 'Complete your order securely.';

// Redirect if not logged in
if (!is_logged_in()) {
    $_SESSION['redirect_after_login'] = SITE_URL . '/checkout.php';
    redirect(SITE_URL . '/auth/login.php');
}

$userId = current_user_id();
$db = GeusGalore\Database::getInstance();
$cart = new GeusGalore\Cart();

// Get cart data
$cartResult = $cart->getCartForCheckout($userId);

if (!$cartResult['success']) {
    $_SESSION['error_message'] = $cartResult['error'] ?? 'Cart validation failed';
    redirect(SITE_URL . '/cart.php');
}

$cartData = $cartResult['cart'];

// Get user info
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);

$message = '';
$messageType = '';

// Handle order submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $orderData = [
        'user_id' => $userId,
        'billing_first_name' => sanitize_input($_POST['billing_first_name']),
        'billing_last_name' => sanitize_input($_POST['billing_last_name']),
        'billing_email' => sanitize_input($_POST['billing_email']),
        'billing_phone' => sanitize_input($_POST['billing_phone']),
        'billing_address' => sanitize_input($_POST['billing_address']),
        'billing_city' => sanitize_input($_POST['billing_city']),
        'billing_state' => sanitize_input($_POST['billing_state']),
        'billing_postal_code' => sanitize_input($_POST['billing_postal_code']),
        'billing_country' => sanitize_input($_POST['billing_country']),
        'payment_method' => sanitize_input($_POST['payment_method']),
        'notes' => sanitize_input($_POST['notes'] ?? ''),
        'subtotal' => $cartData['subtotal'],
        'tax_amount' => 0, // Add tax calculation if needed
        'shipping_amount' => 0, // Free shipping for now
        'total_amount' => $cartData['total'],
        'status' => 'pending'
    ];
    
    // Validate required fields
    $required_fields = ['billing_first_name', 'billing_last_name', 'billing_email', 'billing_phone', 'billing_address', 'billing_city', 'payment_method'];
    $errors = [];
    
    foreach ($required_fields as $field) {
        if (empty($orderData[$field])) {
            $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
        }
    }
    
    if (empty($errors)) {
        try {
            $db->beginTransaction();
            
            // Create order
            $orderId = $db->insert('orders', $orderData);
            
            if ($orderId) {
                // Add order items
                foreach ($cartData['items'] as $item) {
                    $orderItemData = [
                        'order_id' => $orderId,
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'price' => $item['sale_price'] ?: $item['price'],
                        'total' => ($item['sale_price'] ?: $item['price']) * $item['quantity']
                    ];
                    
                    $db->insert('order_items', $orderItemData);
                    
                    // Update product stock if managed
                    if ($item['manage_stock']) {
                        $db->query(
                            "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?",
                            [$item['quantity'], $item['product_id']]
                        );
                    }
                }
                
                // Clear cart
                $cart->clearCart($userId);
                
                $db->commit();
                
                // Redirect to success page
                $_SESSION['success_message'] = 'Order placed successfully! Order #' . str_pad($orderId, 6, '0', STR_PAD_LEFT);
                redirect(SITE_URL . '/user/order-details.php?id=' . $orderId);
            } else {
                throw new Exception('Failed to create order');
            }
        } catch (Exception $e) {
            $db->rollback();
            $message = 'Failed to place order: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'error';
    }
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 100px 0 60px; position: relative; overflow: hidden;">
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center; max-width: 800px; margin: 0 auto;">
            <h1 style="font-size: clamp(2.5rem, 6vw, 4rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                <i class="fas fa-credit-card"></i> Checkout
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 600px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                Complete your order securely
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <?php if ($message): ?>
        <div style="background: <?php echo $messageType === 'success' ? 'var(--success)' : 'var(--danger)'; ?>; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 30px; text-align: center;">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo $message; ?>
        </div>
    <?php endif; ?>

    <form method="POST">
        <div class="row">
            <!-- Billing Information -->
            <div class="col col-8">
                <div class="modern-card" style="margin-bottom: 30px;">
                    <h2 style="margin-bottom: 25px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-user"></i> Billing Information
                    </h2>
                    
                    <div class="row">
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="form-label">First Name *</label>
                                <input type="text" name="billing_first_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                            </div>
                        </div>
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="form-label">Last Name *</label>
                                <input type="text" name="billing_last_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="form-label">Email Address *</label>
                                <input type="email" name="billing_email" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            </div>
                        </div>
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="form-label">Phone Number *</label>
                                <input type="tel" name="billing_phone" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Address *</label>
                        <input type="text" name="billing_address" class="form-control" 
                               value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="row">
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="form-label">City *</label>
                                <input type="text" name="billing_city" class="form-control" required>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="form-label">State/Province</label>
                                <input type="text" name="billing_state" class="form-control">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="form-label">Postal Code</label>
                                <input type="text" name="billing_postal_code" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Country *</label>
                        <select name="billing_country" class="form-control" required>
                            <option value="">Select Country</option>
                            <option value="PG" selected>Papua New Guinea</option>
                            <option value="AU">Australia</option>
                            <option value="NZ">New Zealand</option>
                            <option value="US">United States</option>
                            <option value="CA">Canada</option>
                            <option value="GB">United Kingdom</option>
                        </select>
                    </div>
                </div>
                
                <!-- Payment Method -->
                <div class="modern-card" style="margin-bottom: 30px;">
                    <h2 style="margin-bottom: 25px; background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-credit-card"></i> Payment Method
                    </h2>
                    
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <label style="display: flex; align-items: center; gap: 15px; padding: 15px; border: 2px solid var(--cream); border-radius: 10px; cursor: pointer; transition: all 0.3s ease;">
                            <input type="radio" name="payment_method" value="bank_transfer" required>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 5px;">
                                    <i class="fas fa-university"></i> Bank Transfer
                                </div>
                                <div style="font-size: 14px; color: var(--medium-brown);">
                                    Transfer payment to our bank account
                                </div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 15px; padding: 15px; border: 2px solid var(--cream); border-radius: 10px; cursor: pointer; transition: all 0.3s ease;">
                            <input type="radio" name="payment_method" value="cash_on_delivery" required>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 5px;">
                                    <i class="fas fa-money-bill"></i> Cash on Delivery
                                </div>
                                <div style="font-size: 14px; color: var(--medium-brown);">
                                    Pay when you receive your order
                                </div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 15px; padding: 15px; border: 2px solid var(--cream); border-radius: 10px; cursor: pointer; transition: all 0.3s ease;">
                            <input type="radio" name="payment_method" value="mobile_money" required>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 5px;">
                                    <i class="fas fa-mobile-alt"></i> Mobile Money
                                </div>
                                <div style="font-size: 14px; color: var(--medium-brown);">
                                    Pay via mobile money transfer
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Order Notes -->
                <div class="modern-card">
                    <h3 style="margin-bottom: 20px; color: var(--charcoal); font-weight: 700;">
                        <i class="fas fa-sticky-note"></i> Order Notes (Optional)
                    </h3>
                    <textarea name="notes" class="form-control" rows="4" 
                              placeholder="Any special instructions for your order..."></textarea>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="col col-4">
                <div class="modern-card" style="position: sticky; top: 20px;">
                    <h3 style="margin-bottom: 25px; background: var(--gradient-forest); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-receipt"></i> Order Summary
                    </h3>
                    
                    <!-- Order Items -->
                    <div style="margin-bottom: 25px;">
                        <?php foreach ($cartData['items'] as $item): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--cream);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--charcoal); font-size: 14px; margin-bottom: 3px;">
                                        <?php echo htmlspecialchars($item['name']); ?>
                                    </div>
                                    <div style="font-size: 12px; color: var(--medium-brown);">
                                        Qty: <?php echo $item['quantity']; ?> × <?php echo format_currency($item['sale_price'] ?: $item['price']); ?>
                                    </div>
                                </div>
                                <div style="font-weight: 600; color: var(--coral);">
                                    <?php echo format_currency(($item['sale_price'] ?: $item['price']) * $item['quantity']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Totals -->
                    <div style="display: flex; flex-direction: column; gap: 12px; margin-bottom: 25px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600;">Subtotal:</span>
                            <span style="font-weight: 600; color: var(--coral);">
                                <?php echo format_currency($cartData['subtotal']); ?>
                            </span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600;">Shipping:</span>
                            <span style="font-weight: 600; color: var(--success);">FREE</span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600;">Tax:</span>
                            <span style="font-weight: 600;">K0.00</span>
                        </div>
                        
                        <hr style="border: none; border-top: 2px solid var(--cream); margin: 10px 0;">
                        
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: var(--gradient-sunset); color: white; border-radius: 10px;">
                            <span style="font-weight: 700; font-size: 18px;">Total:</span>
                            <span style="font-weight: 900; font-size: 24px;">
                                <?php echo format_currency($cartData['total']); ?>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Place Order Button -->
                    <button type="submit" class="btn btn-primary" style="width: 100%; font-size: 18px; padding: 15px;">
                        <i class="fas fa-check-circle"></i> Place Order
                    </button>
                    
                    <!-- Security Info -->
                    <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.05); border-radius: 8px;">
                        <div style="color: var(--success); margin-bottom: 8px;">
                            <i class="fas fa-shield-alt" style="font-size: 24px;"></i>
                        </div>
                        <div style="font-size: 12px; color: var(--medium-brown); font-weight: 600;">
                            Secure Checkout<br>
                            Your information is protected
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
/* Payment method selection styling */
input[type="radio"]:checked + div {
    color: var(--coral) !important;
}

label:has(input[type="radio"]:checked) {
    border-color: var(--coral) !important;
    background: rgba(255, 107, 107, 0.05) !important;
}

label:hover {
    border-color: var(--coral) !important;
    background: rgba(255, 107, 107, 0.02) !important;
}
</style>

<script>
// Form validation and submission
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Order...';
    submitBtn.disabled = true;
});

// Auto-fill country based on user location (optional)
document.addEventListener('DOMContentLoaded', function() {
    // You can add geolocation-based country detection here
});
</script>

<?php include 'includes/footer.php'; ?>
