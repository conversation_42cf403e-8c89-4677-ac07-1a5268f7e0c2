<?php
require_once '../config/config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
    exit;
}

$cartId = intval($input['cart_id'] ?? 0);
$quantity = intval($input['quantity'] ?? 0);

if ($cartId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid cart ID']);
    exit;
}

if ($quantity < 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid quantity']);
    exit;
}

try {
    $cart = new GeusGalore\Cart();
    $result = $cart->updateCartItem($cartId, $quantity, current_user_id());
    
    if ($result['success']) {
        $cartData = $cart->getCartTotal(current_user_id());
        $cartCount = $cart->getCartItemCount(current_user_id());
        
        echo json_encode([
            'success' => true,
            'message' => $quantity > 0 ? 'Cart updated successfully' : 'Item removed from cart',
            'cart_count' => $cartCount,
            'cart_total' => $cartData['total'],
            'cart_subtotal' => $cartData['subtotal']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => $result['error']
        ]);
    }
} catch (Exception $e) {
    error_log('Update cart error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'An error occurred while updating cart']);
}
?>
