<?php
require_once 'config/config.php';
$pageTitle = 'Style Test - Beautiful & Colorful Design';
include 'includes/header.php';
?>

<div class="container" style="margin: 40px auto; padding: 0 20px;">
    <div class="card" style="padding: 40px; text-align: center; margin-bottom: 30px;">
        <h1 style="background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 48px; font-weight: 800;">
            🎨 Beautiful & Colorful Design Test 🎨
        </h1>
        <p style="font-size: 18px; color: var(--medium-brown); margin-bottom: 30px;">
            Testing the new colorful Papua New Guinea inspired design
        </p>
        
        <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-bottom: 40px;">
            <a href="<?php echo SITE_URL; ?>" class="btn btn-primary">🏠 Homepage</a>
            <a href="<?php echo SITE_URL; ?>/auth/login.php" class="btn btn-secondary">🔑 Login</a>
            <a href="<?php echo SITE_URL; ?>/auth/register.php" class="btn btn-outline">👤 Register</a>
            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-success">🛍️ Shop</a>
            <a href="<?php echo SITE_URL; ?>/admin/login.php" class="btn btn-info">⚙️ Admin</a>
        </div>
    </div>
    
    <!-- Color Palette Display -->
    <div class="card" style="padding: 30px; margin-bottom: 30px;">
        <h2 style="text-align: center; margin-bottom: 30px; background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            🌈 Papua New Guinea Color Palette
        </h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px;">
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--coral); border-radius: 50%; margin: 0 auto 10px; box-shadow: var(--shadow-md);"></div>
                <p style="font-weight: 600; color: var(--coral);">Coral</p>
            </div>
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--sunset-orange); border-radius: 50%; margin: 0 auto 10px; box-shadow: var(--shadow-md);"></div>
                <p style="font-weight: 600; color: var(--sunset-orange);">Sunset Orange</p>
            </div>
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--golden-yellow); border-radius: 50%; margin: 0 auto 10px; box-shadow: var(--shadow-md);"></div>
                <p style="font-weight: 600; color: var(--golden-yellow);">Golden Yellow</p>
            </div>
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--emerald); border-radius: 50%; margin: 0 auto 10px; box-shadow: var(--shadow-md);"></div>
                <p style="font-weight: 600; color: var(--emerald);">Emerald</p>
            </div>
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--ocean-blue); border-radius: 50%; margin: 0 auto 10px; box-shadow: var(--shadow-md);"></div>
                <p style="font-weight: 600; color: var(--ocean-blue);">Ocean Blue</p>
            </div>
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--deep-blue); border-radius: 50%; margin: 0 auto 10px; box-shadow: var(--shadow-md);"></div>
                <p style="font-weight: 600; color: var(--deep-blue);">Deep Blue</p>
            </div>
        </div>
    </div>
    
    <!-- Gradient Examples -->
    <div class="card" style="padding: 30px; margin-bottom: 30px;">
        <h2 style="text-align: center; margin-bottom: 30px; background: var(--gradient-forest); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            🎨 Beautiful Gradients
        </h2>
        <div class="row">
            <div class="col col-3">
                <div style="height: 120px; background: var(--gradient-sunset); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Sunset Gradient
                </div>
            </div>
            <div class="col col-3">
                <div style="height: 120px; background: var(--gradient-ocean); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Ocean Gradient
                </div>
            </div>
            <div class="col col-3">
                <div style="height: 120px; background: var(--gradient-forest); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Forest Gradient
                </div>
            </div>
            <div class="col col-3">
                <div style="height: 120px; background: var(--gradient-warm); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Warm Gradient
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mobile Responsiveness Test -->
    <div class="card" style="padding: 30px; margin-bottom: 30px;">
        <h2 style="text-align: center; margin-bottom: 30px; background: var(--gradient-warm); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            📱 Mobile Responsive Test
        </h2>
        <p style="text-align: center; margin-bottom: 20px; color: var(--medium-brown);">
            Resize your browser window to test mobile responsiveness
        </p>
        <div class="row">
            <div class="col col-4">
                <div class="card" style="padding: 20px; text-align: center;">
                    <i class="fas fa-mobile-alt" style="font-size: 40px; color: var(--coral); margin-bottom: 15px;"></i>
                    <h3>Mobile First</h3>
                    <p>Optimized for mobile devices</p>
                </div>
            </div>
            <div class="col col-4">
                <div class="card" style="padding: 20px; text-align: center;">
                    <i class="fas fa-tablet-alt" style="font-size: 40px; color: var(--emerald); margin-bottom: 15px;"></i>
                    <h3>Tablet Ready</h3>
                    <p>Perfect on tablets</p>
                </div>
            </div>
            <div class="col col-4">
                <div class="card" style="padding: 20px; text-align: center;">
                    <i class="fas fa-desktop" style="font-size: 40px; color: var(--deep-blue); margin-bottom: 15px;"></i>
                    <h3>Desktop Optimized</h3>
                    <p>Beautiful on desktop</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Login/Register Visibility Test -->
    <div class="card" style="padding: 30px; background: var(--gradient-sunset); color: white; text-align: center;">
        <h2 style="margin-bottom: 20px;">🔐 Authentication Test</h2>
        <p style="margin-bottom: 30px; font-size: 18px;">
            Check if Login/Register buttons are visible in the header above
        </p>
        <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
            <a href="<?php echo SITE_URL; ?>/auth/login.php" class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white;">
                <i class="fas fa-sign-in-alt"></i> Test Login
            </a>
            <a href="<?php echo SITE_URL; ?>/auth/register.php" class="btn" style="background: white; color: var(--coral);">
                <i class="fas fa-user-plus"></i> Test Register
            </a>
        </div>
    </div>
</div>

<script>
console.log('🎨 Style test page loaded!');
console.log('✅ If you see colorful gradients and beautiful styling, the update worked!');

// Test mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const menuToggle = document.querySelector('.menu-toggle');
    if (menuToggle) {
        console.log('✅ Mobile menu toggle found');
    } else {
        console.log('❌ Mobile menu toggle not found');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
