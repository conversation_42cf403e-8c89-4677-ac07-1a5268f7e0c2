<?php
require_once '../config/config.php';

$pageTitle = 'Shop Products';
$pageDescription = 'Browse our wide selection of quality products at Geu\'s Galore.';

// Get filters from URL
$filters = [
    'search' => $_GET['search'] ?? '',
    'category' => $_GET['category'] ?? '',
    'min_price' => $_GET['min_price'] ?? '',
    'max_price' => $_GET['max_price'] ?? '',
    'sort' => $_GET['sort'] ?? 'newest'
];

$page = max(1, intval($_GET['page'] ?? 1));

// Get products
$product = new GeusGalore\Product();
$productData = $product->getAllProducts($page, PRODUCTS_PER_PAGE, $filters);

// Get categories for filter
$db = GeusGalore\Database::getInstance();
$categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");

// Get current category name if filtering by category
$currentCategory = null;
if ($filters['category']) {
    $currentCategory = $db->fetch("SELECT name FROM categories WHERE id = ?", [$filters['category']]);
}

include '../includes/header.php';
?>

<!-- Epic Page Header -->
<section style="padding: 120px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); position: relative; overflow: hidden;">
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center;">
            <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                <?php if ($currentCategory): ?>
                    <i class="fas fa-tag"></i>
                    Category: <?php echo htmlspecialchars($currentCategory['name']); ?>
                <?php elseif ($filters['search']): ?>
                    <i class="fas fa-search"></i>
                    Search Results
                <?php else: ?>
                    <i class="fas fa-shopping-bag"></i>
                    All Products
                <?php endif; ?>
            </div>
            <h1 style="font-size: clamp(2.5rem, 6vw, 4.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                <?php if ($currentCategory): ?>
                    <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"><?php echo htmlspecialchars($currentCategory['name']); ?></span> Products
                <?php elseif ($filters['search']): ?>
                    Search <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Results</span>
                <?php else: ?>
                    Discover Amazing <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Products</span>
                <?php endif; ?>
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 700px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                <?php if ($filters['search']): ?>
                    Results for "<strong><?php echo htmlspecialchars($filters['search']); ?></strong>" -
                <?php endif; ?>
                Showing <?php echo count($productData['products']); ?> of <?php echo $productData['total']; ?> quality products
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col col-3">
            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 30px; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 15px 35px rgba(0,0,0,0.1); margin-bottom: 30px; position: sticky; top: 120px;">
                <h3 style="margin-bottom: 25px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 22px; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-filter"></i> Filters
                </h3>
                
                <form method="GET" id="filterForm">
                    <!-- Keep search term -->
                    <?php if ($filters['search']): ?>
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($filters['search']); ?>">
                    <?php endif; ?>
                    
                    <!-- Categories -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: var(--dark-brown);">Categories</h4>
                        <div>
                            <label style="display: block; margin-bottom: 8px; cursor: pointer;">
                                <input type="radio" name="category" value="" 
                                       <?php echo empty($filters['category']) ? 'checked' : ''; ?>
                                       onchange="document.getElementById('filterForm').submit();">
                                All Categories
                            </label>
                            <?php foreach ($categories as $category): ?>
                                <label style="display: block; margin-bottom: 8px; cursor: pointer;">
                                    <input type="radio" name="category" value="<?php echo $category['id']; ?>"
                                           <?php echo $filters['category'] == $category['id'] ? 'checked' : ''; ?>
                                           onchange="document.getElementById('filterForm').submit();">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Price Range -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: var(--dark-brown);">Price Range</h4>
                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <input type="number" name="min_price" placeholder="Min" 
                                   value="<?php echo htmlspecialchars($filters['min_price']); ?>"
                                   style="flex: 1; padding: 8px; border: 1px solid var(--cream); border-radius: 3px;">
                            <input type="number" name="max_price" placeholder="Max"
                                   value="<?php echo htmlspecialchars($filters['max_price']); ?>"
                                   style="flex: 1; padding: 8px; border: 1px solid var(--cream); border-radius: 3px;">
                        </div>
                        <button type="submit" class="btn btn-outline" style="width: 100%; padding: 8px;">
                            Apply
                        </button>
                    </div>
                    
                    <!-- Sort -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: var(--dark-brown);">Sort By</h4>
                        <select name="sort" onchange="document.getElementById('filterForm').submit();"
                                style="width: 100%; padding: 10px; border: 1px solid var(--cream); border-radius: 3px;">
                            <option value="newest" <?php echo $filters['sort'] === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                            <option value="price_low" <?php echo $filters['sort'] === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo $filters['sort'] === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                            <option value="name" <?php echo $filters['sort'] === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                        </select>
                    </div>
                    
                    <!-- Clear Filters -->
                    <?php if (array_filter($filters)): ?>
                        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-secondary" style="width: 100%;">
                            Clear All Filters
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col col-9">
            <?php if (empty($productData['products'])): ?>
                <div style="text-align: center; padding: 60px 20px;">
                    <i class="fas fa-search" style="font-size: 48px; color: var(--light-brown); margin-bottom: 20px;"></i>
                    <h3 style="color: var(--dark-brown); margin-bottom: 15px;">No Products Found</h3>
                    <p style="color: var(--light-brown); margin-bottom: 30px;">
                        Try adjusting your search criteria or browse our categories.
                    </p>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary">
                        View All Products
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($productData['products'] as $product): ?>
                        <div class="col col-4" style="margin-bottom: 30px;">
                            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; overflow: hidden; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                                 onmouseover="this.style.transform='translateY(-20px) scale(1.03)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                                 onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">

                                <!-- Gradient Border Effect -->
                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb); border-radius: 30px 30px 0 0;"></div>

                                <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $product['slug']; ?>">
                                    <?php if ($product['primary_image']): ?>
                                        <div style="position: relative; overflow: hidden;">
                                            <img src="<?php echo UPLOAD_URL . 'products/' . $product['primary_image']; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 style="width: 100%; height: 220px; object-fit: cover;">
                                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                                        </div>
                                    <?php else: ?>
                                        <div style="width: 100%; height: 220px; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%); display: flex; align-items: center; justify-content: center; position: relative;">
                                            <i class="fas fa-image" style="font-size: 60px; color: rgba(255,255,255,0.8);"></i>
                                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                                        </div>
                                    <?php endif; ?>
                                </a>
                                <div style="padding: 25px;">
                                    <h3 style="font-size: 20px; font-weight: 800; margin-bottom: 12px; color: var(--charcoal);">
                                        <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $product['slug']; ?>" style="text-decoration: none; color: inherit;">
                                            <?php echo htmlspecialchars($product['name']); ?>
                                        </a>
                                    </h3>
                                    <?php if ($product['short_description']): ?>
                                        <p style="color: var(--charcoal); margin-bottom: 20px; font-size: 15px; line-height: 1.6; font-weight: 500; opacity: 0.8;">
                                            <?php echo htmlspecialchars(substr($product['short_description'], 0, 90)) . '...'; ?>
                                        </p>
                                    <?php endif; ?>
                                    <div style="font-size: 24px; font-weight: 900; margin-bottom: 20px; color: var(--coral);">
                                        <?php if ($product['sale_price']): ?>
                                            <span><?php echo format_currency($product['sale_price']); ?></span>
                                            <span style="text-decoration: line-through; color: var(--medium-brown); font-size: 18px; margin-left: 10px;"><?php echo format_currency($product['price']); ?></span>
                                        <?php else: ?>
                                            <?php echo format_currency($product['price']); ?>
                                        <?php endif; ?>
                                    </div>
                                    <div style="display: flex; gap: 12px;">
                                        <button style="flex: 1; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%); color: white; border: none; padding: 15px 20px; border-radius: 50px; font-weight: 700; cursor: pointer; transition: all 0.4s ease; display: flex; align-items: center; justify-content: center; gap: 10px; font-size: 16px; position: relative; overflow: hidden;"
                                                class="add-to-cart"
                                                data-product-id="<?php echo $product['id']; ?>"
                                                onmouseover="this.style.transform='translateY(-3px) scale(1.05)'; this.style.boxShadow='0 15px 35px rgba(255,107,107,0.4)'"
                                                onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='none'">
                                            <i class="fas fa-cart-plus"></i>
                                            Add to Cart
                                        </button>
                                        <a href="<?php echo SITE_URL; ?>/quote/request.php?product=<?php echo $product['id']; ?>"
                                           style="background: rgba(255,255,255,0.1); color: var(--charcoal); border: 2px solid rgba(255,255,255,0.3); padding: 15px; border-radius: 50px; text-decoration: none; transition: all 0.4s ease; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px);"
                                           onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px) scale(1.05)'"
                                           onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(0) scale(1)'">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($productData['pages'] > 1): ?>
                    <div style="text-align: center; margin-top: 40px;">
                        <div style="display: inline-flex; gap: 5px;">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                                   class="btn btn-outline">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($productData['pages'], $page + 2); $i++): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                   class="btn <?php echo $i === $page ? 'btn-primary' : 'btn-outline'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $productData['pages']): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                                   class="btn btn-outline">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .col-3, .col-4, .col-9 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    section {
        padding: 60px 0 !important;
    }

    div[style*="onmouseover"] {
        transform: none !important;
        transition: none !important;
    }

    div[style*="onmouseover"]:hover {
        transform: none !important;
    }

    div[style*="position: sticky"] {
        position: relative !important;
        top: auto !important;
    }
}

@media (max-width: 480px) {
    section {
        padding: 40px 0 !important;
    }

    div[style*="padding: 25px"] {
        padding: 20px !important;
    }

    div[style*="padding: 30px"] {
        padding: 20px !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
