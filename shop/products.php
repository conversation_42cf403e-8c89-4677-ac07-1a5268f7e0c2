<?php
require_once '../config/config.php';

$pageTitle = 'Shop Products';
$pageDescription = 'Browse our wide selection of quality products at Geu\'s Galore.';

// Get filters from URL
$filters = [
    'search' => $_GET['search'] ?? '',
    'category' => $_GET['category'] ?? '',
    'min_price' => $_GET['min_price'] ?? '',
    'max_price' => $_GET['max_price'] ?? '',
    'sort' => $_GET['sort'] ?? 'newest'
];

$page = max(1, intval($_GET['page'] ?? 1));

// Get products
$product = new GeusGalore\Product();
$productData = $product->getAllProducts($page, PRODUCTS_PER_PAGE, $filters);

// Get categories for filter
$db = GeusGalore\Database::getInstance();
$categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");

// Get current category name if filtering by category
$currentCategory = null;
if ($filters['category']) {
    $currentCategory = $db->fetch("SELECT name FROM categories WHERE id = ?", [$filters['category']]);
}

include '../includes/header.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Epic Products Page CSS - Complete Self-Contained Styling */

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* Container System */
        .products-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .products-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
            align-items: flex-start;
        }

        .products-col {
            padding: 0 10px;
        }

        .products-col-3 { flex: 0 0 18%; max-width: 18%; }
        .products-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
        .products-col-9 { flex: 0 0 82%; max-width: 82%; }
        .products-col-12 { flex: 0 0 100%; max-width: 100%; }

        /* Epic Hero Section */
        .products-hero {
            padding: 120px 0 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            position: relative;
            overflow: hidden;
        }

        .products-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        .products-hero-content {
            position: relative;
            z-index: 10;
            text-align: center;
        }

        .products-hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(20px);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            margin-bottom: 30px;
            font-size: 16px;
            font-weight: 700;
            border: 2px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            animation: slideInDown 1s ease-out;
        }

        .products-hero-title {
            font-size: clamp(2.5rem, 6vw, 4.5rem);
            color: white;
            font-weight: 900;
            margin-bottom: 25px;
            text-shadow: 0 0 40px rgba(255,255,255,0.5);
            animation: slideInUp 1s ease-out 0.2s both;
        }

        .products-hero-gradient-text {
            background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .products-hero-subtitle {
            font-size: clamp(1.2rem, 3vw, 1.6rem);
            color: rgba(255,255,255,0.9);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 500;
            animation: slideInUp 1s ease-out 0.4s both;
        }

        /* Floating Animation */
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        @keyframes slideInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Main Content Area */
        .products-main {
            margin: 0 auto 80px;
            position: relative;
            z-index: 10;
            padding-top: 60px;
            clear: both;
            overflow: hidden;
        }

        /* Clearfix for proper layout */
        .products-row::after {
            content: "";
            display: table;
            clear: both;
        }

        /* Epic Sidebar */
        .products-sidebar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 18px;
            padding: 20px 15px;
            border: 2px solid rgba(255,255,255,0.3);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            position: sticky;
            top: 100px;
            transition: all 0.3s ease;
            width: 100%;
            max-width: 240px;
            margin-right: 20px;
            z-index: 5;
        }

        .products-sidebar:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.12);
        }

        /* Mobile Filter Toggle */
        .products-mobile-filter-toggle {
            display: none;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 15px 20px;
            border: 2px solid rgba(255,255,255,0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            align-items: center;
            justify-content: space-between;
            font-weight: 700;
            color: #2c3e50;
        }

        .products-mobile-filter-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .products-mobile-filter-toggle i {
            transition: transform 0.3s ease;
            color: #667eea;
        }

        .products-mobile-filter-toggle.active i {
            transform: rotate(180deg);
        }

        .products-mobile-filter-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .products-mobile-filter-content.active {
            max-height: 1000px;
        }

        .products-sidebar-title {
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 18px;
            font-weight: 800;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .products-filter-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
        }

        .products-filter-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .products-filter-title {
            margin-bottom: 12px;
            color: #2c3e50;
            font-weight: 700;
            font-size: 14px;
        }

        .products-filter-option {
            display: block;
            margin-bottom: 8px;
            cursor: pointer;
            padding: 6px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 13px;
        }

        .products-filter-option:hover {
            background: rgba(102,126,234,0.1);
            transform: translateX(3px);
        }

        .products-filter-option input[type="radio"] {
            margin-right: 8px;
            accent-color: #667eea;
        }

        .products-price-inputs {
            display: flex;
            gap: 12px;
            margin-bottom: 15px;
        }

        .products-price-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid rgba(0,0,0,0.1);
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.8);
        }

        .products-price-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
            transform: translateY(-2px);
        }

        .products-sort-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(0,0,0,0.1);
            border-radius: 12px;
            font-size: 14px;
            background: rgba(255,255,255,0.8);
            transition: all 0.3s ease;
        }

        .products-sort-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
        }

        /* Epic Buttons */
        .products-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            font-size: 14px;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .products-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .products-btn:hover::before {
            left: 100%;
        }

        .products-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .products-btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102,126,234,0.4);
        }

        .products-btn-outline {
            background: rgba(255,255,255,0.1);
            color: #2c3e50;
            border: 2px solid rgba(102,126,234,0.3);
        }

        .products-btn-outline:hover {
            background: rgba(102,126,234,0.1);
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .products-btn-secondary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255,107,107,0.3);
        }

        .products-btn-secondary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255,107,107,0.4);
        }

        .products-btn-full {
            width: 100%;
        }

        /* Epic Product Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
            padding-left: 10px;
        }

        .products-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.4s ease;
            position: relative;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            animation: slideInUp 0.8s ease-out;
            height: fit-content;
            z-index: 1;
        }

        .products-card:hover {
            transform: translateY(-8px) scale(1.01);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
            z-index: 2;
        }

        .products-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb);
            border-radius: 25px 25px 0 0;
        }

        .products-card-image {
            position: relative;
            overflow: hidden;
            height: 250px;
        }

        .products-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.6s ease;
        }

        .products-card:hover .products-card-image img {
            transform: scale(1.1);
        }

        .products-card-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        .products-card-placeholder {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .products-card-placeholder i {
            font-size: 60px;
            color: rgba(102,126,234,0.6);
        }

        .products-card-content {
            padding: 30px;
        }

        .products-card-title {
            font-size: 20px;
            font-weight: 800;
            margin-bottom: 15px;
            color: #2c3e50;
            line-height: 1.3;
        }

        .products-card-title a {
            text-decoration: none;
            color: inherit;
            transition: color 0.3s ease;
        }

        .products-card-title a:hover {
            color: #667eea;
        }

        .products-card-description {
            color: #5a6c7d;
            margin-bottom: 20px;
            font-size: 15px;
            line-height: 1.6;
            font-weight: 500;
        }

        .products-card-price {
            font-size: 28px;
            font-weight: 900;
            margin-bottom: 25px;
            color: #ff6b6b;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .products-card-price-sale {
            text-decoration: line-through;
            color: #95a5a6;
            font-size: 20px;
            font-weight: 600;
        }

        .products-card-actions {
            display: flex;
            gap: 12px;
        }

        .products-card-btn-cart {
            flex: 1;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 16px;
            position: relative;
            overflow: hidden;
        }

        .products-card-btn-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .products-card-btn-cart:hover::before {
            left: 100%;
        }

        .products-card-btn-cart:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255,107,107,0.4);
        }

        .products-card-btn-quote {
            background: rgba(102,126,234,0.1);
            color: #667eea;
            border: 2px solid rgba(102,126,234,0.3);
            padding: 15px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.4s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .products-card-btn-quote:hover {
            background: rgba(102,126,234,0.2);
            border-color: #667eea;
            transform: translateY(-3px) scale(1.05);
        }

        /* Empty State */
        .products-empty {
            text-align: center;
            padding: 80px 20px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            border: 2px solid rgba(255,255,255,0.3);
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .products-empty-icon {
            font-size: 80px;
            color: rgba(102,126,234,0.3);
            margin-bottom: 30px;
        }

        .products-empty-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 28px;
            font-weight: 800;
        }

        .products-empty-text {
            color: #5a6c7d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        /* Pagination */
        .products-pagination {
            text-align: center;
            margin-top: 50px;
        }

        .products-pagination-list {
            display: inline-flex;
            gap: 8px;
            align-items: center;
        }

        .products-pagination-btn {
            padding: 12px 18px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .products-pagination-btn-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .products-pagination-btn-inactive {
            background: rgba(255,255,255,0.8);
            color: #2c3e50;
            border-color: rgba(102,126,234,0.2);
        }

        .products-pagination-btn-inactive:hover {
            background: rgba(102,126,234,0.1);
            border-color: #667eea;
            transform: translateY(-2px);
        }

        /* Shimmer Animation */
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
        }

        @media (max-width: 1200px) {
            .products-col-3 { flex: 0 0 22%; max-width: 22%; }
            .products-col-9 { flex: 0 0 78%; max-width: 78%; }
            .products-sidebar {
                max-width: 220px;
                padding: 18px 12px;
            }
            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 18px;
                padding-left: 5px;
            }
        }

        @media (max-width: 992px) {
            .products-col-3, .products-col-9 {
                flex: 0 0 100%;
                max-width: 100%;
            }

            .products-sidebar {
                display: none;
                position: relative !important;
                top: auto !important;
                max-width: 100%;
                margin-bottom: 20px;
            }

            .products-mobile-filter-toggle {
                display: flex;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .products-hero {
                padding: 80px 0 60px;
            }

            .products-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .products-card:hover {
                transform: none;
            }

            .products-sidebar {
                padding: 25px;
            }

            .products-card-content {
                padding: 25px;
            }
        }

        @media (max-width: 480px) {
            .products-hero {
                padding: 60px 0 40px;
            }

            .products-container {
                padding: 0 15px;
            }

            .products-sidebar {
                padding: 20px;
            }

            .products-card-content {
                padding: 20px;
            }

            .products-card-actions {
                flex-direction: column;
            }

            .products-card-btn-quote {
                padding: 12px;
            }
        }
    </style>
</head>
<body>

<!-- Epic Hero Section -->
<section class="products-hero">
    <div class="products-container">
        <div class="products-hero-content">
            <div class="products-hero-badge">
                <?php if ($currentCategory): ?>
                    <i class="fas fa-tag"></i>
                    Category: <?php echo htmlspecialchars($currentCategory['name']); ?>
                <?php elseif ($filters['search']): ?>
                    <i class="fas fa-search"></i>
                    Search Results
                <?php else: ?>
                    <i class="fas fa-shopping-bag"></i>
                    All Products
                <?php endif; ?>
            </div>
            <h1 class="products-hero-title">
                <?php if ($currentCategory): ?>
                    <span class="products-hero-gradient-text"><?php echo htmlspecialchars($currentCategory['name']); ?></span> Products
                <?php elseif ($filters['search']): ?>
                    Search <span class="products-hero-gradient-text">Results</span>
                <?php else: ?>
                    Discover Amazing <span class="products-hero-gradient-text">Products</span>
                <?php endif; ?>
            </h1>
            <p class="products-hero-subtitle">
                <?php if ($filters['search']): ?>
                    Results for "<strong><?php echo htmlspecialchars($filters['search']); ?></strong>" -
                <?php endif; ?>
                Showing <?php echo count($productData['products']); ?> of <?php echo $productData['total']; ?> quality products
            </p>
        </div>
    </div>
</section>

<div class="products-container products-main">
    <div class="products-row">
        <!-- Epic Sidebar Filters -->
        <div class="products-col products-col-3">
            <div class="products-sidebar">
                <h3 class="products-sidebar-title">
                    <i class="fas fa-filter"></i> Filters
                </h3>

                <form method="GET" id="filterForm">
                    <!-- Keep search term -->
                    <?php if ($filters['search']): ?>
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($filters['search']); ?>">
                    <?php endif; ?>

                    <!-- Categories -->
                    <div class="products-filter-section">
                        <h4 class="products-filter-title">Categories</h4>
                        <div>
                            <label class="products-filter-option">
                                <input type="radio" name="category" value=""
                                       <?php echo empty($filters['category']) ? 'checked' : ''; ?>
                                       onchange="document.getElementById('filterForm').submit();">
                                All Categories
                            </label>
                            <?php foreach ($categories as $category): ?>
                                <label class="products-filter-option">
                                    <input type="radio" name="category" value="<?php echo $category['id']; ?>"
                                           <?php echo $filters['category'] == $category['id'] ? 'checked' : ''; ?>
                                           onchange="document.getElementById('filterForm').submit();">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="products-filter-section">
                        <h4 class="products-filter-title">Price Range</h4>
                        <div class="products-price-inputs">
                            <input type="number" name="min_price" placeholder="Min"
                                   value="<?php echo htmlspecialchars($filters['min_price']); ?>"
                                   class="products-price-input">
                            <input type="number" name="max_price" placeholder="Max"
                                   value="<?php echo htmlspecialchars($filters['max_price']); ?>"
                                   class="products-price-input">
                        </div>
                        <button type="submit" class="products-btn products-btn-outline products-btn-full">
                            <i class="fas fa-search"></i> Apply
                        </button>
                    </div>

                    <!-- Sort -->
                    <div class="products-filter-section">
                        <h4 class="products-filter-title">Sort By</h4>
                        <select name="sort" onchange="document.getElementById('filterForm').submit();"
                                class="products-sort-select">
                            <option value="newest" <?php echo $filters['sort'] === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                            <option value="price_low" <?php echo $filters['sort'] === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo $filters['sort'] === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                            <option value="name" <?php echo $filters['sort'] === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                        </select>
                    </div>

                    <!-- Clear Filters -->
                    <?php if (array_filter($filters)): ?>
                        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="products-btn products-btn-secondary products-btn-full">
                            <i class="fas fa-times"></i> Clear All Filters
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Epic Products Grid -->
        <div class="products-col products-col-9">
            <!-- Mobile Filter Toggle -->
            <div class="products-mobile-filter-toggle" onclick="toggleMobileFilter()">
                <span><i class="fas fa-filter"></i> Filters & Sort</span>
                <i class="fas fa-chevron-down"></i>
            </div>

            <!-- Mobile Filter Content -->
            <div class="products-mobile-filter-content" id="mobileFilterContent">
                <div class="products-sidebar" id="mobileSidebar">
                    <h3 class="products-sidebar-title">
                        <i class="fas fa-filter"></i> Filters
                    </h3>

                    <form method="GET" id="mobileFilterForm">
                        <!-- Keep search term -->
                        <?php if ($filters['search']): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($filters['search']); ?>">
                        <?php endif; ?>

                        <!-- Categories -->
                        <div class="products-filter-section">
                            <h4 class="products-filter-title">Categories</h4>
                            <div>
                                <label class="products-filter-option">
                                    <input type="radio" name="category" value=""
                                           <?php echo empty($filters['category']) ? 'checked' : ''; ?>
                                           onchange="document.getElementById('mobileFilterForm').submit();">
                                    All Categories
                                </label>
                                <?php foreach ($categories as $category): ?>
                                    <label class="products-filter-option">
                                        <input type="radio" name="category" value="<?php echo $category['id']; ?>"
                                               <?php echo $filters['category'] == $category['id'] ? 'checked' : ''; ?>
                                               onchange="document.getElementById('mobileFilterForm').submit();">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Price Range -->
                        <div class="products-filter-section">
                            <h4 class="products-filter-title">Price Range</h4>
                            <div class="products-price-inputs">
                                <input type="number" name="min_price" placeholder="Min"
                                       value="<?php echo htmlspecialchars($filters['min_price']); ?>"
                                       class="products-price-input">
                                <input type="number" name="max_price" placeholder="Max"
                                       value="<?php echo htmlspecialchars($filters['max_price']); ?>"
                                       class="products-price-input">
                            </div>
                            <button type="submit" class="products-btn products-btn-outline products-btn-full">
                                <i class="fas fa-search"></i> Apply
                            </button>
                        </div>

                        <!-- Sort -->
                        <div class="products-filter-section">
                            <h4 class="products-filter-title">Sort By</h4>
                            <select name="sort" onchange="document.getElementById('mobileFilterForm').submit();"
                                    class="products-sort-select">
                                <option value="newest" <?php echo $filters['sort'] === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                                <option value="price_low" <?php echo $filters['sort'] === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                                <option value="price_high" <?php echo $filters['sort'] === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                                <option value="name" <?php echo $filters['sort'] === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                            </select>
                        </div>

                        <!-- Clear Filters -->
                        <?php if (array_filter($filters)): ?>
                            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="products-btn products-btn-secondary products-btn-full">
                                <i class="fas fa-times"></i> Clear All Filters
                            </a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <?php if (empty($productData['products'])): ?>
                <!-- Epic Empty State -->
                <div class="products-empty">
                    <div class="products-empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="products-empty-title">No Products Found</h3>
                    <p class="products-empty-text">
                        <?php if ($filters['search']): ?>
                            No products match your search for "<?php echo htmlspecialchars($filters['search']); ?>".
                        <?php elseif ($filters['category']): ?>
                            No products found in this category.
                        <?php else: ?>
                            Try adjusting your search criteria or browse our categories.
                        <?php endif; ?>
                    </p>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" class="products-btn products-btn-primary">
                        <i class="fas fa-arrow-left"></i> View All Products
                    </a>
                </div>
            <?php else: ?>
                <!-- Epic Products Grid -->
                <div class="products-grid">
                    <?php foreach ($productData['products'] as $product): ?>
                        <div class="products-card">
                            <!-- Epic Product Image -->
                            <div class="products-card-image">
                                <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $product['slug']; ?>">
                                    <?php if ($product['primary_image']): ?>
                                        <img src="<?php echo UPLOAD_URL . 'products/' . $product['primary_image']; ?>"
                                             alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="products-card-placeholder">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                </a>
                            </div>

                            <!-- Epic Product Content -->
                            <div class="products-card-content">
                                <h3 class="products-card-title">
                                    <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $product['slug']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </a>
                                </h3>

                                <p class="products-card-description">
                                    <?php echo htmlspecialchars(substr($product['description'], 0, 120)) . '...'; ?>
                                </p>

                                <!-- Epic Price Display -->
                                <div class="products-card-price">
                                    <?php echo format_currency($product['sale_price'] ?: $product['price']); ?>
                                    <?php if ($product['sale_price']): ?>
                                        <span class="products-card-price-sale">
                                            <?php echo format_currency($product['price']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Epic Actions -->
                                <div class="products-card-actions">
                                    <button onclick="addToCart(<?php echo $product['id']; ?>)"
                                            class="products-card-btn-cart add-to-cart"
                                            data-product-id="<?php echo $product['id']; ?>">
                                        <i class="fas fa-shopping-cart"></i>
                                        Add to Cart
                                    </button>

                                    <a href="<?php echo SITE_URL; ?>/quote/request.php?product=<?php echo $product['id']; ?>"
                                       class="products-card-btn-quote">
                                        <i class="fas fa-quote-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Epic Pagination -->
                <?php if ($productData['pages'] > 1): ?>
                    <div class="products-pagination">
                        <div class="products-pagination-list">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                   class="products-pagination-btn products-pagination-btn-inactive">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($productData['pages'], $page + 2); $i++): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                   class="products-pagination-btn <?php echo $i === $page ? 'products-pagination-btn-active' : 'products-pagination-btn-inactive'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $productData['pages']): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                   class="products-pagination-btn products-pagination-btn-inactive">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Epic Cart Functionality -->
<script>
// Epic Add to Cart Function
async function addToCart(productId) {
    try {
        const response = await fetch('<?php echo SITE_URL; ?>/api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add',
                product_id: productId,
                quantity: 1
            })
        });

        const result = await response.json();

        if (result.success) {
            // Epic success animation
            showNotification('Product added to cart! 🛒', 'success');
            updateCartCount();
        } else {
            showNotification(result.message || 'Failed to add product to cart', 'error');
        }
    } catch (error) {
        console.error('Cart error:', error);
        showNotification('Something went wrong. Please try again.', 'error');
    }
}

// Epic Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.epic-notification');
    if (existing) existing.remove();

    // Create notification
    const notification = document.createElement('div');
    notification.className = `epic-notification epic-notification-${type}`;
    notification.innerHTML = `
        <div class="epic-notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        background: ${type === 'success' ? 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' :
                    type === 'error' ? 'linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%)' :
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
        color: white;
        padding: 15px 25px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(255,255,255,0.2);
        font-weight: 600;
        animation: slideInRight 0.5s ease-out;
        cursor: pointer;
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-in forwards';
        setTimeout(() => notification.remove(), 500);
    }, 3000);

    // Click to remove
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOutRight 0.5s ease-in forwards';
        setTimeout(() => notification.remove(), 500);
    });
}

// Update cart count in header
function updateCartCount() {
    fetch('<?php echo SITE_URL; ?>/api/cart.php?action=count')
        .then(response => response.json())
        .then(data => {
            const cartCount = document.querySelector('.cart-count');
            if (cartCount && data.count) {
                cartCount.textContent = data.count;
                cartCount.style.display = data.count > 0 ? 'inline' : 'none';
            }
        })
        .catch(error => console.error('Cart count error:', error));
}

// Add notification animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .epic-notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .epic-notification:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    }
`;
document.head.appendChild(style);

// Initialize cart count on page load
document.addEventListener('DOMContentLoaded', updateCartCount);

// Epic Mobile Filter Toggle Function
function toggleMobileFilter() {
    const toggle = document.querySelector('.products-mobile-filter-toggle');
    const content = document.querySelector('.products-mobile-filter-content');
    const sidebar = document.querySelector('#mobileSidebar');

    toggle.classList.toggle('active');
    content.classList.toggle('active');

    if (content.classList.contains('active')) {
        sidebar.style.display = 'block';
    } else {
        setTimeout(() => {
            sidebar.style.display = 'none';
        }, 300);
    }
}

// Close mobile filter when window is resized to desktop
window.addEventListener('resize', function() {
    if (window.innerWidth > 992) {
        const toggle = document.querySelector('.products-mobile-filter-toggle');
        const content = document.querySelector('.products-mobile-filter-content');
        const sidebar = document.querySelector('#mobileSidebar');

        toggle.classList.remove('active');
        content.classList.remove('active');
        sidebar.style.display = 'none';
    }
});
</script>

</body>
</html>

<?php include '../includes/footer.php'; ?>
