<?php
require_once '../config/config.php';

$pageTitle = 'Shop Products';
$pageDescription = 'Browse our wide selection of quality products at Geu\'s Galore.';

// Get filters from URL
$filters = [
    'search' => $_GET['search'] ?? '',
    'category' => $_GET['category'] ?? '',
    'min_price' => $_GET['min_price'] ?? '',
    'max_price' => $_GET['max_price'] ?? '',
    'sort' => $_GET['sort'] ?? 'newest'
];

$page = max(1, intval($_GET['page'] ?? 1));

// Get products
$product = new GeusGalore\Product();
$productData = $product->getAllProducts($page, PRODUCTS_PER_PAGE, $filters);

// Get categories for filter
$db = GeusGalore\Database::getInstance();
$categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");

// Get current category name if filtering by category
$currentCategory = null;
if ($filters['category']) {
    $currentCategory = $db->fetch("SELECT name FROM categories WHERE id = ?", [$filters['category']]);
}

include '../includes/header.php';
?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1>
            <?php if ($currentCategory): ?>
                🏷️ <?php echo htmlspecialchars($currentCategory['name']); ?>
            <?php elseif ($filters['search']): ?>
                🔍 Search Results
            <?php else: ?>
                🛍️ All Products
            <?php endif; ?>
        </h1>
        <p>
            <?php if ($filters['search']): ?>
                Results for "<?php echo htmlspecialchars($filters['search']); ?>" -
            <?php endif; ?>
            Showing <?php echo count($productData['products']); ?> of <?php echo $productData['total']; ?> products
        </p>
    </div>
</div>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col col-3">
            <div class="modern-card" style="margin-bottom: 30px; position: sticky; top: 120px;">
                <h3 style="margin-bottom: 25px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 22px; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-filter"></i> Filters
                </h3>
                
                <form method="GET" id="filterForm">
                    <!-- Keep search term -->
                    <?php if ($filters['search']): ?>
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($filters['search']); ?>">
                    <?php endif; ?>
                    
                    <!-- Categories -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: var(--dark-brown);">Categories</h4>
                        <div>
                            <label style="display: block; margin-bottom: 8px; cursor: pointer;">
                                <input type="radio" name="category" value="" 
                                       <?php echo empty($filters['category']) ? 'checked' : ''; ?>
                                       onchange="document.getElementById('filterForm').submit();">
                                All Categories
                            </label>
                            <?php foreach ($categories as $category): ?>
                                <label style="display: block; margin-bottom: 8px; cursor: pointer;">
                                    <input type="radio" name="category" value="<?php echo $category['id']; ?>"
                                           <?php echo $filters['category'] == $category['id'] ? 'checked' : ''; ?>
                                           onchange="document.getElementById('filterForm').submit();">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Price Range -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: var(--dark-brown);">Price Range</h4>
                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <input type="number" name="min_price" placeholder="Min" 
                                   value="<?php echo htmlspecialchars($filters['min_price']); ?>"
                                   style="flex: 1; padding: 8px; border: 1px solid var(--cream); border-radius: 3px;">
                            <input type="number" name="max_price" placeholder="Max"
                                   value="<?php echo htmlspecialchars($filters['max_price']); ?>"
                                   style="flex: 1; padding: 8px; border: 1px solid var(--cream); border-radius: 3px;">
                        </div>
                        <button type="submit" class="btn btn-outline" style="width: 100%; padding: 8px;">
                            Apply
                        </button>
                    </div>
                    
                    <!-- Sort -->
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: var(--dark-brown);">Sort By</h4>
                        <select name="sort" onchange="document.getElementById('filterForm').submit();"
                                style="width: 100%; padding: 10px; border: 1px solid var(--cream); border-radius: 3px;">
                            <option value="newest" <?php echo $filters['sort'] === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                            <option value="price_low" <?php echo $filters['sort'] === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo $filters['sort'] === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                            <option value="name" <?php echo $filters['sort'] === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                        </select>
                    </div>
                    
                    <!-- Clear Filters -->
                    <?php if (array_filter($filters)): ?>
                        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-secondary" style="width: 100%;">
                            Clear All Filters
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col col-9">
            <?php if (empty($productData['products'])): ?>
                <div style="text-align: center; padding: 60px 20px;">
                    <i class="fas fa-search" style="font-size: 48px; color: var(--light-brown); margin-bottom: 20px;"></i>
                    <h3 style="color: var(--dark-brown); margin-bottom: 15px;">No Products Found</h3>
                    <p style="color: var(--light-brown); margin-bottom: 30px;">
                        Try adjusting your search criteria or browse our categories.
                    </p>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary">
                        View All Products
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($productData['products'] as $product): ?>
                        <div class="col col-4" style="margin-bottom: 30px;">
                            <div class="card">
                                <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $product['slug']; ?>">
                                    <?php if ($product['primary_image']): ?>
                                        <img src="<?php echo UPLOAD_URL . 'products/' . $product['primary_image']; ?>" 
                                             alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                             class="card-img">
                                    <?php else: ?>
                                        <div class="card-img" style="background-color: var(--cream); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-image" style="font-size: 48px; color: var(--light-brown);"></i>
                                        </div>
                                    <?php endif; ?>
                                </a>
                                <div class="card-body">
                                    <h3 class="card-title">
                                        <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $product['slug']; ?>">
                                            <?php echo htmlspecialchars($product['name']); ?>
                                        </a>
                                    </h3>
                                    <?php if ($product['short_description']): ?>
                                        <p class="card-text">
                                            <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                                        </p>
                                    <?php endif; ?>
                                    <div class="card-price">
                                        <?php if ($product['sale_price']): ?>
                                            <span class="price-sale"><?php echo format_currency($product['sale_price']); ?></span>
                                            <span class="price-original"><?php echo format_currency($product['price']); ?></span>
                                        <?php else: ?>
                                            <?php echo format_currency($product['price']); ?>
                                        <?php endif; ?>
                                    </div>
                                    <div style="display: flex; gap: 10px;">
                                        <button class="btn btn-primary add-to-cart" 
                                                data-product-id="<?php echo $product['id']; ?>"
                                                style="flex: 1;">
                                            <i class="fas fa-cart-plus"></i> Add to Cart
                                        </button>
                                        <a href="<?php echo SITE_URL; ?>/quote/request.php?product=<?php echo $product['id']; ?>" 
                                           class="btn btn-outline">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($productData['pages'] > 1): ?>
                    <div style="text-align: center; margin-top: 40px;">
                        <div style="display: inline-flex; gap: 5px;">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                                   class="btn btn-outline">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($productData['pages'], $page + 2); $i++): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                   class="btn <?php echo $i === $page ? 'btn-primary' : 'btn-outline'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $productData['pages']): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                                   class="btn btn-outline">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
