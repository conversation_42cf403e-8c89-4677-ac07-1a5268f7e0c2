<?php
require_once '../config/config.php';

// Get product by slug
$slug = $_GET['slug'] ?? '';
if (empty($slug)) {
    redirect(SITE_URL . '/shop/products.php');
}

$product = new GeusGalore\Product();
$productData = $product->getProductBySlug($slug);

if (!$productData) {
    redirect(SITE_URL . '/shop/products.php');
}

$pageTitle = $productData['name'];
$pageDescription = $productData['short_description'] ?: 'View product details for ' . $productData['name'] . ' at Geu\'s Galore.';

// Get related products
$relatedProducts = $product->getRelatedProducts($productData['category_id'], $productData['id'], 4);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* Epic Product Detail Page Styling */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --white: #FFFFFF;
            --cream: #E6DCD3;
            --light-brown: #B4A79E;
            --medium-brown: #BDA18C;
            --dark-brown: #3F352C;
            --charcoal: #2C3E50;
            --coral: #FF6B6B;
            --emerald: #2ECC71;
            --deep-blue: #3498DB;
            --sunset-orange: #FF8E53;
            --light-peach: #FFE5D9;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--charcoal);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Epic Header */
        .epic-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(102,126,234,0.2);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 900;
            color: white;
            text-decoration: none;
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
            align-items: center;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        /* Epic Product Section */
        .product-hero {
            padding: 60px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ffd93d 100%);
            position: relative;
            overflow: hidden;
        }
        
        .breadcrumb {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(20px);
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            margin-bottom: 30px;
            display: inline-block;
            border: 2px solid rgba(255,255,255,0.2);
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
            margin-top: 40px;
        }
        
        .product-images {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 30px;
            border: 2px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .main-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .product-info {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 40px;
            border: 2px solid rgba(255,255,255,0.2);
            position: relative;
        }
        
        .product-title {
            font-size: 36px;
            font-weight: 900;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }
        
        .product-price {
            font-size: 32px;
            font-weight: 900;
            color: white;
            margin-bottom: 25px;
        }
        
        .product-description {
            color: rgba(255,255,255,0.9);
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .epic-btn {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            box-shadow: 0 15px 35px rgba(67,233,123,0.4);
            margin-right: 15px;
            margin-bottom: 15px;
        }
        
        .epic-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 25px 50px rgba(67,233,123,0.6);
        }
        
        .epic-btn-outline {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }
        
        .epic-btn-outline:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
        }
        
        /* Related Products */
        .related-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%);
        }
        
        .section-title {
            text-align: center;
            font-size: 42px;
            font-weight: 900;
            color: white;
            margin-bottom: 50px;
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
        }
        
        .product-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.2);
            transition: all 0.5s ease;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-20px) scale(1.03);
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
        }
        
        .card-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .card-content {
            padding: 25px;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: 800;
            color: white;
            margin-bottom: 10px;
        }
        
        .card-price {
            font-size: 24px;
            font-weight: 900;
            color: white;
            margin-bottom: 15px;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .nav-links {
                display: none;
            }
            
            .product-title {
                font-size: 28px;
            }
            
            .product-price {
                font-size: 24px;
            }
            
            .epic-btn {
                width: 100%;
                justify-content: center;
                margin-bottom: 15px;
            }
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body>
    <!-- Epic Header -->
    <header class="epic-header">
        <div class="container">
            <div class="header-content">
                <a href="<?php echo SITE_URL; ?>" class="logo">
                    <?php echo SITE_NAME; ?>
                </a>
                <nav class="nav-links">
                    <a href="<?php echo SITE_URL; ?>"><i class="fas fa-home"></i> Home</a>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php"><i class="fas fa-shopping-bag"></i> Shop</a>
                    <a href="<?php echo SITE_URL; ?>/pages/about.php"><i class="fas fa-info-circle"></i> About</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Product Hero Section -->
    <section class="product-hero">
        <div class="container">
            <div class="breadcrumb">
                <i class="fas fa-home"></i> Home > Shop > <?php echo htmlspecialchars($productData['name']); ?>
            </div>
            
            <div class="product-grid">
                <!-- Product Images -->
                <div class="product-images">
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb); border-radius: 30px 30px 0 0;"></div>
                    
                    <?php if (!empty($productData['images'])): ?>
                        <img src="<?php echo UPLOAD_URL . 'products/' . $productData['images'][0]['image_path']; ?>" 
                             alt="<?php echo htmlspecialchars($productData['name']); ?>" 
                             class="main-image">
                    <?php else: ?>
                        <div style="width: 100%; height: 400px; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%); border-radius: 20px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-image" style="font-size: 80px; color: rgba(255,255,255,0.8);"></i>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Product Info -->
                <div class="product-info">
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb); border-radius: 30px 30px 0 0;"></div>
                    
                    <h1 class="product-title"><?php echo htmlspecialchars($productData['name']); ?></h1>
                    
                    <div class="product-price">
                        <?php if ($productData['sale_price']): ?>
                            <span><?php echo format_currency($productData['sale_price']); ?></span>
                            <span style="text-decoration: line-through; color: rgba(255,255,255,0.6); font-size: 24px; margin-left: 15px;">
                                <?php echo format_currency($productData['price']); ?>
                            </span>
                        <?php else: ?>
                            <?php echo format_currency($productData['price']); ?>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($productData['description']): ?>
                        <div class="product-description">
                            <?php echo nl2br(htmlspecialchars($productData['description'])); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div style="margin-top: 30px;">
                        <button class="epic-btn add-to-cart" data-product-id="<?php echo $productData['id']; ?>">
                            <i class="fas fa-cart-plus"></i>
                            Add to Cart
                        </button>
                        
                        <a href="<?php echo SITE_URL; ?>/quote/request.php?product=<?php echo $productData['id']; ?>" 
                           class="epic-btn epic-btn-outline">
                            <i class="fas fa-file-invoice"></i>
                            Request Quote
                        </a>
                    </div>
                    
                    <?php if ($productData['category_name']): ?>
                        <div style="margin-top: 25px; padding-top: 25px; border-top: 1px solid rgba(255,255,255,0.2);">
                            <span style="color: rgba(255,255,255,0.8); font-weight: 600;">
                                <i class="fas fa-tag" style="margin-right: 8px;"></i>
                                Category: <?php echo htmlspecialchars($productData['category_name']); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products Section -->
    <?php if (!empty($relatedProducts)): ?>
    <section class="related-section">
        <div class="container">
            <h2 class="section-title">
                Related <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Products</span>
            </h2>

            <div class="products-grid">
                <?php foreach ($relatedProducts as $relatedProduct): ?>
                    <div class="product-card">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb); border-radius: 30px 30px 0 0;"></div>

                        <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $relatedProduct['slug']; ?>">
                            <?php if ($relatedProduct['primary_image']): ?>
                                <img src="<?php echo UPLOAD_URL . 'products/' . $relatedProduct['primary_image']; ?>"
                                     alt="<?php echo htmlspecialchars($relatedProduct['name']); ?>"
                                     class="card-image">
                            <?php else: ?>
                                <div style="width: 100%; height: 200px; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-image" style="font-size: 40px; color: rgba(255,255,255,0.8);"></i>
                                </div>
                            <?php endif; ?>
                        </a>

                        <div class="card-content">
                            <h3 class="card-title">
                                <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $relatedProduct['slug']; ?>"
                                   style="text-decoration: none; color: inherit;">
                                    <?php echo htmlspecialchars($relatedProduct['name']); ?>
                                </a>
                            </h3>

                            <div class="card-price">
                                <?php if ($relatedProduct['sale_price']): ?>
                                    <span><?php echo format_currency($relatedProduct['sale_price']); ?></span>
                                    <span style="text-decoration: line-through; color: rgba(255,255,255,0.6); font-size: 16px; margin-left: 10px;">
                                        <?php echo format_currency($relatedProduct['price']); ?>
                                    </span>
                                <?php else: ?>
                                    <?php echo format_currency($relatedProduct['price']); ?>
                                <?php endif; ?>
                            </div>

                            <button class="epic-btn add-to-cart" data-product-id="<?php echo $relatedProduct['id']; ?>"
                                    style="width: 100%; justify-content: center; padding: 15px 25px; font-size: 16px;">
                                <i class="fas fa-cart-plus"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Epic Footer -->
    <footer style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 60px 0 30px; color: white;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 40px;">
                <h3 style="font-size: 32px; font-weight: 900; margin-bottom: 15px;">
                    <?php echo SITE_NAME; ?>
                </h3>
                <p style="color: rgba(255,255,255,0.8); font-size: 18px;">
                    Papua New Guinea's Premier Online Store
                </p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px;">
                <div>
                    <h4 style="font-size: 20px; font-weight: 700; margin-bottom: 20px; color: #ffd93d;">Quick Links</h4>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <a href="<?php echo SITE_URL; ?>" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Home</a>
                        <a href="<?php echo SITE_URL; ?>/shop/products.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Shop</a>
                        <a href="<?php echo SITE_URL; ?>/pages/about.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">About Us</a>
                        <a href="<?php echo SITE_URL; ?>/quote/request.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Request Quote</a>
                    </div>
                </div>

                <div>
                    <h4 style="font-size: 20px; font-weight: 700; margin-bottom: 20px; color: #ffd93d;">Contact Info</h4>
                    <div style="color: rgba(255,255,255,0.8); line-height: 1.8;">
                        <div><i class="fas fa-envelope" style="margin-right: 10px; color: #ffd93d;"></i> <?php echo SITE_EMAIL; ?></div>
                        <div><i class="fas fa-phone" style="margin-right: 10px; color: #ffd93d;"></i> +************</div>
                        <div><i class="fas fa-map-marker-alt" style="margin-right: 10px; color: #ffd93d;"></i> Papua New Guinea</div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.2);">
                <p style="color: rgba(255,255,255,0.6);">
                    &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Add to Cart -->
    <script>
        // Add to cart functionality
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                const productId = this.dataset.productId;
                const originalText = this.innerHTML;

                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
                this.disabled = true;

                fetch('<?php echo SITE_URL; ?>/ajax/add-to-cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        quantity: 1
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.innerHTML = '<i class="fas fa-check"></i> Added!';
                        this.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';

                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }, 2000);

                        // Show success message
                        showNotification('Product added to cart!', 'success');
                    } else {
                        this.innerHTML = originalText;
                        this.disabled = false;
                        showNotification(data.error || 'Failed to add product to cart', 'error');
                    }
                })
                .catch(error => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                    showNotification('An error occurred. Please try again.', 'error');
                });
            });
        });

        // Simple notification system
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' : 'linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%)'};
                color: white;
                padding: 15px 25px;
                border-radius: 50px;
                font-weight: 600;
                z-index: 10000;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                transform: translateX(400px);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
