<?php
namespace GeusGalore;

class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function register($data) {
        // Validate input
        $errors = $this->validateRegistration($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Check if user already exists
        if ($this->emailExists($data['email'])) {
            return ['success' => false, 'errors' => ['email' => 'Email already exists']];
        }
        
        if ($this->usernameExists($data['username'])) {
            return ['success' => false, 'errors' => ['username' => 'Username already exists']];
        }
        
        // Hash password and generate verification token
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        $verificationToken = generate_random_string(64);
        
        $userData = [
            'username' => sanitize_input($data['username']),
            'email' => sanitize_input($data['email']),
            'password' => $hashedPassword,
            'first_name' => sanitize_input($data['first_name']),
            'last_name' => sanitize_input($data['last_name']),
            'phone' => sanitize_input($data['phone'] ?? ''),
            'verification_token' => $verificationToken
        ];
        
        try {
            $userId = $this->db->insert('users', $userData);
            
            // Send verification email
            $this->sendVerificationEmail($userData['email'], $userData['first_name'], $verificationToken);
            
            return [
                'success' => true, 
                'user_id' => $userId,
                'message' => 'Registration successful! Please check your email to verify your account.'
            ];
        } catch (\Exception $e) {
            error_log("User registration error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Registration failed. Please try again.']];
        }
    }
    
    public function login($email, $password) {
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = ? AND email_verified = 1",
            [$email]
        );
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            
            return ['success' => true, 'user' => $user];
        }
        
        return ['success' => false, 'error' => 'Invalid email or password'];
    }
    
    public function logout() {
        unset($_SESSION['user_id']);
        unset($_SESSION['user_email']);
        unset($_SESSION['user_name']);
        session_destroy();
    }
    
    public function verifyEmail($token) {
        $user = $this->db->fetch(
            "SELECT id FROM users WHERE verification_token = ? AND email_verified = 0",
            [$token]
        );
        
        if ($user) {
            $this->db->update(
                'users',
                ['email_verified' => 1, 'verification_token' => null],
                'id = ?',
                [$user['id']]
            );
            return true;
        }
        
        return false;
    }
    
    public function getUserById($id) {
        return $this->db->fetch("SELECT * FROM users WHERE id = ?", [$id]);
    }
    
    public function updateProfile($userId, $data) {
        $errors = $this->validateProfileUpdate($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        $updateData = [
            'first_name' => sanitize_input($data['first_name']),
            'last_name' => sanitize_input($data['last_name']),
            'phone' => sanitize_input($data['phone'] ?? ''),
            'address' => sanitize_input($data['address'] ?? ''),
            'city' => sanitize_input($data['city'] ?? ''),
            'postal_code' => sanitize_input($data['postal_code'] ?? ''),
            'country' => sanitize_input($data['country'] ?? 'Papua New Guinea')
        ];
        
        try {
            $this->db->update('users', $updateData, 'id = ?', [$userId]);
            return ['success' => true, 'message' => 'Profile updated successfully'];
        } catch (\Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Update failed. Please try again.']];
        }
    }
    
    public function changePassword($userId, $currentPassword, $newPassword) {
        $user = $this->getUserById($userId);
        
        if (!password_verify($currentPassword, $user['password'])) {
            return ['success' => false, 'error' => 'Current password is incorrect'];
        }
        
        if (strlen($newPassword) < 6) {
            return ['success' => false, 'error' => 'New password must be at least 6 characters'];
        }
        
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        try {
            $this->db->update('users', ['password' => $hashedPassword], 'id = ?', [$userId]);
            return ['success' => true, 'message' => 'Password changed successfully'];
        } catch (\Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Password change failed. Please try again.'];
        }
    }
    
    private function validateRegistration($data) {
        $errors = [];
        
        if (empty($data['username']) || strlen($data['username']) < 3) {
            $errors['username'] = 'Username must be at least 3 characters';
        }
        
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Valid email is required';
        }
        
        if (empty($data['password']) || strlen($data['password']) < 6) {
            $errors['password'] = 'Password must be at least 6 characters';
        }
        
        if (empty($data['first_name'])) {
            $errors['first_name'] = 'First name is required';
        }
        
        if (empty($data['last_name'])) {
            $errors['last_name'] = 'Last name is required';
        }
        
        return $errors;
    }
    
    private function validateProfileUpdate($data) {
        $errors = [];
        
        if (empty($data['first_name'])) {
            $errors['first_name'] = 'First name is required';
        }
        
        if (empty($data['last_name'])) {
            $errors['last_name'] = 'Last name is required';
        }
        
        return $errors;
    }
    
    private function emailExists($email) {
        return $this->db->exists('users', 'email = ?', [$email]);
    }
    
    private function usernameExists($username) {
        return $this->db->exists('users', 'username = ?', [$username]);
    }
    
    private function sendVerificationEmail($email, $name, $token) {
        $verificationUrl = SITE_URL . "/auth/verify.php?token=" . $token;
        
        $subject = "Verify your " . SITE_NAME . " account";
        $message = "
        <html>
        <body>
            <h2>Welcome to " . SITE_NAME . "!</h2>
            <p>Hello {$name},</p>
            <p>Thank you for registering with us. Please click the link below to verify your email address:</p>
            <p><a href='{$verificationUrl}' style='background-color: #3F352C; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Verify Email</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{$verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
            <p>Best regards,<br>The " . SITE_NAME . " Team</p>
        </body>
        </html>
        ";
        
        // Use Email class to send email
        $emailSender = new Email();
        return $emailSender->send($email, $subject, $message);
    }
}
?>
