<?php
namespace GeusGalore;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHP<PERSON>ailer\PHPMailer\Exception;

class Email {
    private $mailer;
    
    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->setupSMTP();
    }
    
    private function setupSMTP() {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = SMTP_ENCRYPTION;
            $this->mailer->Port = SMTP_PORT;
            
            // Default sender
            $this->mailer->setFrom(SITE_EMAIL, SITE_NAME);
            
            // Content type
            $this->mailer->isHTML(true);
            $this->mailer->CharSet = 'UTF-8';
            
        } catch (Exception $e) {
            error_log("Email setup error: " . $e->getMessage());
        }
    }
    
    public function send($to, $subject, $body, $altBody = '') {
        try {
            // Recipients
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($to);
            
            // Content
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $this->getEmailTemplate($body, $subject);
            $this->mailer->AltBody = $altBody ?: strip_tags($body);
            
            $this->mailer->send();
            return true;
            
        } catch (Exception $e) {
            error_log("Email send error: " . $e->getMessage());
            return false;
        }
    }
    
    public function sendQuoteEmail($to, $name, $quoteData) {
        $subject = "Your Quote from " . SITE_NAME . " - Quote #" . $quoteData['quote_number'];
        
        $body = "
        <h2>Thank you for your quote request!</h2>
        <p>Dear {$name},</p>
        <p>We have prepared a quote for you. Please find the details below:</p>
        
        <div style='background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;'>
            <h3>Quote Details</h3>
            <p><strong>Quote Number:</strong> {$quoteData['quote_number']}</p>
            <p><strong>Valid Until:</strong> {$quoteData['valid_until']}</p>
            <p><strong>Total Amount:</strong> " . format_currency($quoteData['total_amount']) . "</p>
        </div>
        
        <h3>Items:</h3>
        <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
            <thead>
                <tr style='background-color: #3F352C; color: white;'>
                    <th style='padding: 10px; text-align: left;'>Product</th>
                    <th style='padding: 10px; text-align: center;'>Quantity</th>
                    <th style='padding: 10px; text-align: right;'>Price</th>
                    <th style='padding: 10px; text-align: right;'>Total</th>
                </tr>
            </thead>
            <tbody>";
        
        foreach ($quoteData['items'] as $item) {
            $body .= "
                <tr style='border-bottom: 1px solid #ddd;'>
                    <td style='padding: 10px;'>{$item['product_name']}</td>
                    <td style='padding: 10px; text-align: center;'>{$item['quantity']}</td>
                    <td style='padding: 10px; text-align: right;'>" . format_currency($item['price']) . "</td>
                    <td style='padding: 10px; text-align: right;'>" . format_currency($item['total']) . "</td>
                </tr>";
        }
        
        $body .= "
            </tbody>
            <tfoot>
                <tr style='background-color: #f8f9fa; font-weight: bold;'>
                    <td colspan='3' style='padding: 10px; text-align: right;'>Total:</td>
                    <td style='padding: 10px; text-align: right;'>" . format_currency($quoteData['total_amount']) . "</td>
                </tr>
            </tfoot>
        </table>
        
        <p>If you have any questions about this quote, please don't hesitate to contact us.</p>
        <p>To proceed with your order, please visit our website or contact us directly.</p>
        
        <p>Best regards,<br>The " . SITE_NAME . " Team</p>
        ";
        
        return $this->send($to, $subject, $body);
    }
    
    public function sendOrderConfirmation($to, $orderData) {
        $subject = "Order Confirmation - Order #" . $orderData['order_number'];
        
        $body = "
        <h2>Order Confirmation</h2>
        <p>Dear {$orderData['customer_name']},</p>
        <p>Thank you for your order! We have received your order and it is being processed.</p>
        
        <div style='background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;'>
            <h3>Order Details</h3>
            <p><strong>Order Number:</strong> {$orderData['order_number']}</p>
            <p><strong>Order Date:</strong> {$orderData['order_date']}</p>
            <p><strong>Total Amount:</strong> " . format_currency($orderData['total_amount']) . "</p>
            <p><strong>Payment Status:</strong> {$orderData['payment_status']}</p>
        </div>
        
        <h3>Items Ordered:</h3>
        <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
            <thead>
                <tr style='background-color: #3F352C; color: white;'>
                    <th style='padding: 10px; text-align: left;'>Product</th>
                    <th style='padding: 10px; text-align: center;'>Quantity</th>
                    <th style='padding: 10px; text-align: right;'>Price</th>
                    <th style='padding: 10px; text-align: right;'>Total</th>
                </tr>
            </thead>
            <tbody>";
        
        foreach ($orderData['items'] as $item) {
            $body .= "
                <tr style='border-bottom: 1px solid #ddd;'>
                    <td style='padding: 10px;'>{$item['product_name']}</td>
                    <td style='padding: 10px; text-align: center;'>{$item['quantity']}</td>
                    <td style='padding: 10px; text-align: right;'>" . format_currency($item['price']) . "</td>
                    <td style='padding: 10px; text-align: right;'>" . format_currency($item['total']) . "</td>
                </tr>";
        }
        
        $body .= "
            </tbody>
        </table>
        
        <h3>Shipping Address:</h3>
        <div style='background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>
            {$orderData['shipping_address']}
        </div>
        
        <p>We will send you another email when your order ships.</p>
        <p>If you have any questions about your order, please contact us.</p>
        
        <p>Best regards,<br>The " . SITE_NAME . " Team</p>
        ";
        
        return $this->send($to, $subject, $body);
    }
    
    public function sendContactForm($name, $email, $subject, $message) {
        $emailSubject = "Contact Form Submission: " . $subject;
        
        $body = "
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> {$name}</p>
        <p><strong>Email:</strong> {$email}</p>
        <p><strong>Subject:</strong> {$subject}</p>
        <p><strong>Message:</strong></p>
        <div style='background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>
            " . nl2br(htmlspecialchars($message)) . "
        </div>
        ";
        
        return $this->send(ADMIN_EMAIL, $emailSubject, $body);
    }
    
    private function getEmailTemplate($content, $title = '') {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$title}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #3F352C; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: white; }
                .footer { background-color: #E6DCD3; padding: 15px; text-align: center; font-size: 12px; color: #666; }
                a { color: #3F352C; }
                .btn { display: inline-block; padding: 10px 20px; background-color: #3F352C; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>" . SITE_NAME . "</h1>
                </div>
                <div class='content'>
                    {$content}
                </div>
                <div class='footer'>
                    <p>&copy; " . date('Y') . " " . SITE_NAME . ". All rights reserved.</p>
                    <p>Papua New Guinea's Premier Online Store</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
?>
