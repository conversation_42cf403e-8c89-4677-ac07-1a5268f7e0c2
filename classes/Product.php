<?php
namespace GeusGalore;

class Product {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function getAllProducts($page = 1, $limit = PRODUCTS_PER_PAGE, $filters = []) {
        $offset = ($page - 1) * $limit;
        $where = ['p.status = "active"'];
        $params = [];
        
        // Apply filters
        if (!empty($filters['category'])) {
            $where[] = 'p.category_id = ?';
            $params[] = $filters['category'];
        }
        
        if (!empty($filters['search'])) {
            $where[] = '(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($filters['min_price'])) {
            $where[] = 'p.price >= ?';
            $params[] = $filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $where[] = 'p.price <= ?';
            $params[] = $filters['max_price'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "
            SELECT p.*, c.name as category_name, 
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE {$whereClause}
            ORDER BY p.featured DESC, p.created_at DESC 
            LIMIT {$limit} OFFSET {$offset}
        ";
        
        $products = $this->db->fetchAll($sql, $params);
        
        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM products p WHERE {$whereClause}";
        $totalResult = $this->db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        return [
            'products' => $products,
            'total' => $total,
            'pages' => ceil($total / $limit),
            'current_page' => $page
        ];
    }
    
    public function getProductById($id) {
        $product = $this->db->fetch("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.id = ? AND p.status = 'active'
        ", [$id]);
        
        if ($product) {
            $product['images'] = $this->getProductImages($id);
        }
        
        return $product;
    }
    
    public function getProductBySlug($slug) {
        $product = $this->db->fetch("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.slug = ? AND p.status = 'active'
        ", [$slug]);
        
        if ($product) {
            $product['images'] = $this->getProductImages($product['id']);
        }
        
        return $product;
    }
    
    public function getFeaturedProducts($limit = 8) {
        return $this->db->fetchAll("
            SELECT p.*, c.name as category_name,
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.featured = 1 AND p.status = 'active'
            ORDER BY p.created_at DESC 
            LIMIT ?
        ", [$limit]);
    }
    
    public function getRelatedProducts($productId, $categoryId, $limit = 4) {
        return $this->db->fetchAll("
            SELECT p.*, c.name as category_name,
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.category_id = ? AND p.id != ? AND p.status = 'active'
            ORDER BY RAND() 
            LIMIT ?
        ", [$categoryId, $productId, $limit]);
    }
    
    public function getProductImages($productId) {
        return $this->db->fetchAll("
            SELECT * FROM product_images 
            WHERE product_id = ? 
            ORDER BY is_primary DESC, sort_order ASC
        ", [$productId]);
    }
    
    public function searchProducts($query, $limit = 20) {
        $searchTerm = '%' . $query . '%';
        return $this->db->fetchAll("
            SELECT p.*, c.name as category_name,
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE (p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?) 
            AND p.status = 'active'
            ORDER BY p.name ASC 
            LIMIT ?
        ", [$searchTerm, $searchTerm, $searchTerm, $limit]);
    }
    
    // Admin methods
    public function createProduct($data) {
        $errors = $this->validateProduct($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Generate slug
        $slug = generate_slug($data['name']);
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->db->exists('products', 'slug = ?', [$slug])) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        $productData = [
            'name' => sanitize_input($data['name']),
            'slug' => $slug,
            'description' => $data['description'],
            'short_description' => sanitize_input($data['short_description']),
            'sku' => sanitize_input($data['sku']),
            'price' => $data['price'],
            'sale_price' => $data['sale_price'] ?: null,
            'stock_quantity' => $data['stock_quantity'],
            'category_id' => $data['category_id'] ?: null,
            'featured' => isset($data['featured']) ? 1 : 0,
            'status' => $data['status']
        ];
        
        try {
            $productId = $this->db->insert('products', $productData);
            return ['success' => true, 'product_id' => $productId];
        } catch (\Exception $e) {
            error_log("Product creation error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Product creation failed']];
        }
    }
    
    public function updateProduct($id, $data) {
        $errors = $this->validateProduct($data);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        $productData = [
            'name' => sanitize_input($data['name']),
            'description' => $data['description'],
            'short_description' => sanitize_input($data['short_description']),
            'sku' => sanitize_input($data['sku']),
            'price' => $data['price'],
            'sale_price' => $data['sale_price'] ?: null,
            'stock_quantity' => $data['stock_quantity'],
            'category_id' => $data['category_id'] ?: null,
            'featured' => isset($data['featured']) ? 1 : 0,
            'status' => $data['status']
        ];
        
        try {
            $this->db->update('products', $productData, 'id = ?', [$id]);
            return ['success' => true];
        } catch (\Exception $e) {
            error_log("Product update error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Product update failed']];
        }
    }
    
    public function deleteProduct($id) {
        try {
            // Delete product images first
            $images = $this->getProductImages($id);
            foreach ($images as $image) {
                $imagePath = UPLOAD_PATH . 'products/' . $image['image_path'];
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            
            $this->db->delete('product_images', 'product_id = ?', [$id]);
            $this->db->delete('products', 'id = ?', [$id]);
            
            return ['success' => true];
        } catch (\Exception $e) {
            error_log("Product deletion error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Product deletion failed'];
        }
    }
    
    public function addProductImage($productId, $imagePath, $altText = '', $isPrimary = false) {
        $imageData = [
            'product_id' => $productId,
            'image_path' => $imagePath,
            'alt_text' => $altText,
            'is_primary' => $isPrimary ? 1 : 0
        ];
        
        // If this is primary, unset other primary images
        if ($isPrimary) {
            $this->db->update('product_images', ['is_primary' => 0], 'product_id = ?', [$productId]);
        }
        
        return $this->db->insert('product_images', $imageData);
    }
    
    private function validateProduct($data) {
        $errors = [];
        
        if (empty($data['name'])) {
            $errors['name'] = 'Product name is required';
        }
        
        if (empty($data['price']) || !is_numeric($data['price']) || $data['price'] <= 0) {
            $errors['price'] = 'Valid price is required';
        }
        
        if (!empty($data['sale_price']) && (!is_numeric($data['sale_price']) || $data['sale_price'] <= 0)) {
            $errors['sale_price'] = 'Sale price must be a valid number';
        }
        
        if (!is_numeric($data['stock_quantity']) || $data['stock_quantity'] < 0) {
            $errors['stock_quantity'] = 'Stock quantity must be a valid number';
        }
        
        return $errors;
    }
}
?>
