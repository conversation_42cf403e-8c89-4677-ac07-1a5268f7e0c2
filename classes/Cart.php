<?php
namespace GeusGalore;

class Cart {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function addToCart($productId, $quantity = 1, $userId = null) {
        $sessionId = session_id();
        
        // Check if product exists and is active
        $product = $this->db->fetch("SELECT * FROM products WHERE id = ? AND status = 'active'", [$productId]);
        if (!$product) {
            return ['success' => false, 'error' => 'Product not found'];
        }
        
        // Check stock
        if ($product['manage_stock'] && $product['stock_quantity'] < $quantity) {
            return ['success' => false, 'error' => 'Insufficient stock'];
        }
        
        // Check if item already exists in cart
        $whereClause = $userId ? 'user_id = ? AND product_id = ?' : 'session_id = ? AND product_id = ?';
        $params = $userId ? [$userId, $productId] : [$sessionId, $productId];
        
        $existingItem = $this->db->fetch("SELECT * FROM cart WHERE {$whereClause}", $params);
        
        if ($existingItem) {
            // Update quantity
            $newQuantity = $existingItem['quantity'] + $quantity;
            
            // Check stock again
            if ($product['manage_stock'] && $product['stock_quantity'] < $newQuantity) {
                return ['success' => false, 'error' => 'Insufficient stock'];
            }
            
            $this->db->update('cart', ['quantity' => $newQuantity], 'id = ?', [$existingItem['id']]);
        } else {
            // Add new item
            $cartData = [
                'user_id' => $userId,
                'session_id' => $sessionId,
                'product_id' => $productId,
                'quantity' => $quantity
            ];
            
            $this->db->insert('cart', $cartData);
        }
        
        return ['success' => true, 'message' => 'Item added to cart'];
    }
    
    public function updateCartItem($cartId, $quantity, $userId = null) {
        $sessionId = session_id();
        
        // Get cart item
        $whereClause = $userId ? 'id = ? AND user_id = ?' : 'id = ? AND session_id = ?';
        $params = $userId ? [$cartId, $userId] : [$cartId, $sessionId];
        
        $cartItem = $this->db->fetch("SELECT * FROM cart WHERE {$whereClause}", $params);
        if (!$cartItem) {
            return ['success' => false, 'error' => 'Cart item not found'];
        }
        
        // Check product stock
        $product = $this->db->fetch("SELECT * FROM products WHERE id = ?", [$cartItem['product_id']]);
        if ($product['manage_stock'] && $product['stock_quantity'] < $quantity) {
            return ['success' => false, 'error' => 'Insufficient stock'];
        }
        
        if ($quantity <= 0) {
            // Remove item
            $this->db->delete('cart', 'id = ?', [$cartId]);
        } else {
            // Update quantity
            $this->db->update('cart', ['quantity' => $quantity], 'id = ?', [$cartId]);
        }
        
        return ['success' => true];
    }
    
    public function removeFromCart($cartId, $userId = null) {
        $sessionId = session_id();
        
        $whereClause = $userId ? 'id = ? AND user_id = ?' : 'id = ? AND session_id = ?';
        $params = $userId ? [$cartId, $userId] : [$cartId, $sessionId];
        
        $this->db->delete('cart', $whereClause, $params);
        
        return ['success' => true];
    }
    
    public function getCartItems($userId = null) {
        $sessionId = session_id();
        
        $whereClause = $userId ? 'c.user_id = ?' : 'c.session_id = ?';
        $params = $userId ? [$userId] : [$sessionId];
        
        return $this->db->fetchAll("
            SELECT c.*, p.name, p.price, p.sale_price, p.slug, p.stock_quantity, p.manage_stock,
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image
            FROM cart c
            JOIN products p ON c.product_id = p.id
            WHERE {$whereClause} AND p.status = 'active'
            ORDER BY c.created_at DESC
        ", $params);
    }
    
    public function getCartTotal($userId = null) {
        $items = $this->getCartItems($userId);
        $total = 0;
        $itemCount = 0;
        
        foreach ($items as $item) {
            $price = $item['sale_price'] ?: $item['price'];
            $total += $price * $item['quantity'];
            $itemCount += $item['quantity'];
        }
        
        return [
            'subtotal' => $total,
            'total' => $total, // Add tax/shipping calculation here if needed
            'item_count' => $itemCount,
            'items' => $items
        ];
    }
    
    public function getCartItemCount($userId = null) {
        $sessionId = session_id();
        
        $whereClause = $userId ? 'c.user_id = ?' : 'c.session_id = ?';
        $params = $userId ? [$userId] : [$sessionId];
        
        $result = $this->db->fetch("
            SELECT SUM(c.quantity) as total_items
            FROM cart c
            JOIN products p ON c.product_id = p.id
            WHERE {$whereClause} AND p.status = 'active'
        ", $params);
        
        return $result['total_items'] ?: 0;
    }
    
    public function clearCart($userId = null) {
        $sessionId = session_id();
        
        $whereClause = $userId ? 'user_id = ?' : 'session_id = ?';
        $params = $userId ? [$userId] : [$sessionId];
        
        $this->db->delete('cart', $whereClause, $params);
        
        return ['success' => true];
    }
    
    public function transferSessionCart($userId) {
        $sessionId = session_id();
        
        // Get session cart items
        $sessionItems = $this->db->fetchAll("SELECT * FROM cart WHERE session_id = ?", [$sessionId]);
        
        foreach ($sessionItems as $item) {
            // Check if user already has this product in cart
            $existingItem = $this->db->fetch(
                "SELECT * FROM cart WHERE user_id = ? AND product_id = ?",
                [$userId, $item['product_id']]
            );
            
            if ($existingItem) {
                // Update quantity
                $newQuantity = $existingItem['quantity'] + $item['quantity'];
                $this->db->update('cart', ['quantity' => $newQuantity], 'id = ?', [$existingItem['id']]);
            } else {
                // Add new item for user
                $this->db->update('cart', ['user_id' => $userId], 'id = ?', [$item['id']]);
            }
        }
        
        // Clear session cart
        $this->db->delete('cart', 'session_id = ? AND user_id IS NULL', [$sessionId]);
    }
    
    public function validateCartStock($userId = null) {
        $items = $this->getCartItems($userId);
        $errors = [];
        
        foreach ($items as $item) {
            if ($item['manage_stock'] && $item['stock_quantity'] < $item['quantity']) {
                $errors[] = [
                    'product_name' => $item['name'],
                    'requested' => $item['quantity'],
                    'available' => $item['stock_quantity']
                ];
            }
        }
        
        return $errors;
    }
    
    public function getCartForCheckout($userId = null) {
        $cartData = $this->getCartTotal($userId);
        $stockErrors = $this->validateCartStock($userId);
        
        if (!empty($stockErrors)) {
            return ['success' => false, 'stock_errors' => $stockErrors];
        }
        
        if (empty($cartData['items'])) {
            return ['success' => false, 'error' => 'Cart is empty'];
        }
        
        return ['success' => true, 'cart' => $cartData];
    }
}
?>
