<?php
require_once '../config/config.php';

$pageTitle = 'My Dashboard';
$pageDescription = 'Manage your account, orders, and preferences at Geu\'s Galore.';

// Redirect if not logged in
if (!is_logged_in()) {
    $_SESSION['redirect_after_login'] = SITE_URL . '/user/dashboard.php';
    redirect(SITE_URL . '/auth/login.php');
}

$userId = current_user_id();
$db = GeusGalore\Database::getInstance();

// Get user info
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);

// Get recent orders
$recentOrders = $db->fetchAll("
    SELECT o.*, COUNT(oi.id) as item_count 
    FROM orders o 
    LEFT JOIN order_items oi ON o.id = oi.order_id 
    WHERE o.user_id = ? 
    GROUP BY o.id 
    ORDER BY o.created_at DESC 
    LIMIT 5
", [$userId]);

// Get cart count
$cart = new GeusGalore\Cart();
$cartCount = $cart->getCartItemCount($userId);

include '../includes/header.php';
?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1>👋 Welcome Back, <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
        <p>Manage your account and track your orders</p>
    </div>
</div>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    <!-- Quick Stats -->
    <div class="row" style="margin-bottom: 40px;">
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-sunset); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo count($recentOrders); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-shopping-bag"></i> Total Orders
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-ocean); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo $cartCount; ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-shopping-cart"></i> Cart Items
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-forest); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo $user['loyalty_points'] ?? 0; ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-star"></i> Loyalty Points
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-warm); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo $user['is_verified'] ? 'Verified' : 'Pending'; ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-shield-check"></i> Account Status
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col col-8">
            <!-- Quick Actions -->
            <div class="modern-card" style="margin-bottom: 30px;">
                <h2 style="margin-bottom: 25px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h2>
                <div class="row">
                    <div class="col col-6" style="margin-bottom: 15px;">
                        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary" style="width: 100%; justify-content: center;">
                            <i class="fas fa-shopping-bag"></i> Continue Shopping
                        </a>
                    </div>
                    <div class="col col-6" style="margin-bottom: 15px;">
                        <a href="<?php echo SITE_URL; ?>/shop/cart.php" class="btn btn-secondary" style="width: 100%; justify-content: center;">
                            <i class="fas fa-shopping-cart"></i> View Cart (<?php echo $cartCount; ?>)
                        </a>
                    </div>
                    <div class="col col-6" style="margin-bottom: 15px;">
                        <a href="<?php echo SITE_URL; ?>/quote/request.php" class="btn btn-outline" style="width: 100%; justify-content: center;">
                            <i class="fas fa-file-invoice"></i> Request Quote
                        </a>
                    </div>
                    <div class="col col-6" style="margin-bottom: 15px;">
                        <a href="<?php echo SITE_URL; ?>/user/profile.php" class="btn btn-info" style="width: 100%; justify-content: center;">
                            <i class="fas fa-user-edit"></i> Edit Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="modern-card">
                <h2 style="margin-bottom: 25px; background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-history"></i> Recent Orders
                </h2>
                
                <?php if (empty($recentOrders)): ?>
                    <div style="text-align: center; padding: 40px 20px; color: var(--medium-brown);">
                        <i class="fas fa-shopping-bag" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3 style="margin-bottom: 15px;">No Orders Yet</h3>
                        <p style="margin-bottom: 25px;">Start shopping to see your orders here</p>
                        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary">
                            <i class="fas fa-shopping-bag"></i> Start Shopping
                        </a>
                    </div>
                <?php else: ?>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, var(--light-peach) 0%, var(--cream) 100%); border-radius: var(--radius-md);">
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: var(--charcoal);">Order #</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: var(--charcoal);">Date</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: var(--charcoal);">Items</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: var(--charcoal);">Total</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: var(--charcoal);">Status</th>
                                    <th style="padding: 15px; text-align: center; font-weight: 600; color: var(--charcoal);">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr style="border-bottom: 1px solid rgba(255,255,255,0.3);">
                                        <td style="padding: 15px; font-weight: 600; color: var(--coral);">
                                            #<?php echo $order['order_number']; ?>
                                        </td>
                                        <td style="padding: 15px; color: var(--charcoal);">
                                            <?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                                        </td>
                                        <td style="padding: 15px; color: var(--charcoal);">
                                            <?php echo $order['item_count']; ?> items
                                        </td>
                                        <td style="padding: 15px; font-weight: 600; color: var(--charcoal);">
                                            <?php echo format_currency($order['total_amount']); ?>
                                        </td>
                                        <td style="padding: 15px;">
                                            <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; 
                                                <?php
                                                switch($order['status']) {
                                                    case 'completed':
                                                        echo 'background: var(--gradient-forest); color: white;';
                                                        break;
                                                    case 'processing':
                                                        echo 'background: var(--gradient-warm); color: white;';
                                                        break;
                                                    case 'pending':
                                                        echo 'background: var(--gradient-ocean); color: white;';
                                                        break;
                                                    default:
                                                        echo 'background: var(--light-brown); color: white;';
                                                }
                                                ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                        <td style="padding: 15px; text-align: center;">
                                            <a href="<?php echo SITE_URL; ?>/user/order-details.php?id=<?php echo $order['id']; ?>" 
                                               class="btn btn-outline" style="padding: 8px 16px; font-size: 14px;">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div style="text-align: center; margin-top: 25px;">
                        <a href="<?php echo SITE_URL; ?>/user/orders.php" class="btn btn-outline">
                            <i class="fas fa-list"></i> View All Orders
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col col-4">
            <!-- Account Info -->
            <div class="modern-card" style="margin-bottom: 30px; text-align: center;">
                <div style="width: 80px; height: 80px; background: var(--gradient-sunset); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 35px; color: white; font-weight: 800;">
                    <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                </div>
                <h3 style="margin-bottom: 10px; color: var(--charcoal);">
                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                </h3>
                <p style="color: var(--medium-brown); margin-bottom: 20px;">
                    <?php echo htmlspecialchars($user['email']); ?>
                </p>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                    <a href="<?php echo SITE_URL; ?>/user/profile.php" class="btn btn-primary" style="padding: 10px 20px; font-size: 14px;">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                    <a href="<?php echo SITE_URL; ?>/auth/logout.php" class="btn btn-outline" style="padding: 10px 20px; font-size: 14px;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="modern-card">
                <h3 style="margin-bottom: 20px; background: var(--gradient-forest); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                    <i class="fas fa-link"></i> Quick Links
                </h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <a href="<?php echo SITE_URL; ?>/user/orders.php" style="display: flex; align-items: center; gap: 12px; padding: 12px 16px; background: rgba(255,255,255,0.7); border-radius: var(--radius-md); text-decoration: none; transition: all 0.3s ease;">
                        <i class="fas fa-shopping-bag" style="color: var(--coral);"></i>
                        <span style="color: var(--charcoal); font-weight: 500;">My Orders</span>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/user/addresses.php" style="display: flex; align-items: center; gap: 12px; padding: 12px 16px; background: rgba(255,255,255,0.7); border-radius: var(--radius-md); text-decoration: none; transition: all 0.3s ease;">
                        <i class="fas fa-map-marker-alt" style="color: var(--emerald);"></i>
                        <span style="color: var(--charcoal); font-weight: 500;">Addresses</span>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/user/wishlist.php" style="display: flex; align-items: center; gap: 12px; padding: 12px 16px; background: rgba(255,255,255,0.7); border-radius: var(--radius-md); text-decoration: none; transition: all 0.3s ease;">
                        <i class="fas fa-heart" style="color: var(--sunset-orange);"></i>
                        <span style="color: var(--charcoal); font-weight: 500;">Wishlist</span>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/user/quotes.php" style="display: flex; align-items: center; gap: 12px; padding: 12px 16px; background: rgba(255,255,255,0.7); border-radius: var(--radius-md); text-decoration: none; transition: all 0.3s ease;">
                        <i class="fas fa-file-invoice" style="color: var(--deep-blue);"></i>
                        <span style="color: var(--charcoal); font-weight: 500;">My Quotes</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Dashboard specific styles */
@media (max-width: 768px) {
    .col-3, .col-4, .col-6, .col-8 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }
    
    table {
        font-size: 14px;
    }
    
    th, td {
        padding: 10px 8px !important;
    }
    
    .btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* Hover effects for quick links */
.modern-card a:hover {
    background: rgba(255, 107, 107, 0.1) !important;
    transform: translateX(5px);
}
</style>

<?php include '../includes/footer.php'; ?>
