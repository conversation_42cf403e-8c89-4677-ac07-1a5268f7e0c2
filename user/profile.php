<?php
require_once '../config/config.php';

$pageTitle = 'My Profile';
$pageDescription = 'Manage your personal information and account settings.';

// Redirect if not logged in
if (!is_logged_in()) {
    $_SESSION['redirect_after_login'] = SITE_URL . '/user/profile.php';
    redirect(SITE_URL . '/auth/login.php');
}

$userId = current_user_id();
$db = GeusGalore\Database::getInstance();
$user_obj = new GeusGalore\User();

// Get user info
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'update_profile') {
            $result = $user_obj->updateProfile($userId, $_POST);
            
            if ($result['success']) {
                $message = $result['message'];
                $messageType = 'success';
                // Refresh user data
                $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
            } else {
                $message = $result['errors']['general'] ?? 'Update failed';
                $messageType = 'error';
            }
        }
        
        elseif ($_POST['action'] === 'change_password') {
            $result = $user_obj->changePassword($userId, $_POST['current_password'], $_POST['new_password']);
            
            if ($result['success']) {
                $message = 'Password changed successfully!';
                $messageType = 'success';
            } else {
                $message = $result['error'];
                $messageType = 'error';
            }
        }
    }
}

include '../includes/header.php';
?>

<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); padding: 100px 0 60px; position: relative; overflow: hidden;">
    <!-- Animated Background Elements -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1;">
        <div style="position: absolute; top: 20%; left: 10%; width: 100px; height: 100px; background: white; border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div style="position: absolute; top: 60%; right: 15%; width: 150px; height: 150px; background: white; border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; bottom: 20%; left: 20%; width: 80px; height: 80px; background: white; border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>
    
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center; max-width: 800px; margin: 0 auto;">
            <h1 style="font-size: clamp(2.5rem, 6vw, 4rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                <i class="fas fa-user-circle"></i> My Profile
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 600px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                Manage your personal information and account settings
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <?php if ($message): ?>
        <div style="background: <?php echo $messageType === 'success' ? 'var(--success)' : 'var(--danger)'; ?>; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 30px; text-align: center;">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Profile Information -->
        <div class="col col-6">
            <div class="modern-card" style="margin-bottom: 30px;">
                <h2 style="margin-bottom: 25px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-user"></i> Personal Information
                </h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="row">
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="form-label">First Name</label>
                                <input type="text" name="first_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                            </div>
                        </div>
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="form-label">Last Name</label>
                                <input type="text" name="last_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Username</label>
                        <input type="text" name="username" class="form-control" 
                               value="<?php echo htmlspecialchars($user['username']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email Address</label>
                        <input type="email" name="email" class="form-control" 
                               value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        <?php if (!$user['email_verified']): ?>
                            <div style="color: var(--warning); font-size: 12px; margin-top: 5px;">
                                <i class="fas fa-exclamation-triangle"></i> Email not verified
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Phone Number</label>
                        <input type="tel" name="phone" class="form-control" 
                               value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Address</label>
                        <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-save"></i> Update Profile
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Account Security -->
        <div class="col col-6">
            <div class="modern-card" style="margin-bottom: 30px;">
                <h2 style="margin-bottom: 25px; background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-shield-alt"></i> Account Security
                </h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="form-group">
                        <label class="form-label">Current Password</label>
                        <input type="password" name="current_password" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">New Password</label>
                        <input type="password" name="new_password" class="form-control" minlength="6" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirm New Password</label>
                        <input type="password" name="confirm_password" class="form-control" minlength="6" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                </form>
            </div>
            
            <!-- Account Status -->
            <div class="modern-card">
                <h3 style="margin-bottom: 20px; color: var(--charcoal); font-weight: 700;">
                    <i class="fas fa-info-circle"></i> Account Status
                </h3>
                
                <div style="display: flex; flex-direction: column; gap: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                        <span style="font-weight: 600;">Email Verification</span>
                        <span class="status-badge status-<?php echo $user['email_verified'] ? 'active' : 'inactive'; ?>">
                            <?php echo $user['email_verified'] ? 'Verified' : 'Pending'; ?>
                        </span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                        <span style="font-weight: 600;">Account Status</span>
                        <span class="status-badge status-<?php echo $user['status']; ?>">
                            <?php echo ucfirst($user['status']); ?>
                        </span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                        <span style="font-weight: 600;">Member Since</span>
                        <span style="font-weight: 600; color: var(--coral);">
                            <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                        </span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                        <span style="font-weight: 600;">Loyalty Points</span>
                        <span style="font-weight: 600; color: var(--success);">
                            <?php echo number_format($user['loyalty_points'] ?? 0); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="modern-card" style="text-align: center; margin-top: 30px;">
        <h3 style="margin-bottom: 25px; color: var(--charcoal); font-weight: 700;">
            <i class="fas fa-bolt"></i> Quick Actions
        </h3>
        <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
            <a href="dashboard.php" class="btn btn-outline">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="orders.php" class="btn btn-outline">
                <i class="fas fa-shopping-bag"></i> My Orders
            </a>
            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary">
                <i class="fas fa-shopping-cart"></i> Continue Shopping
            </a>
            <a href="../auth/logout.php" class="btn" style="background: var(--danger); color: white;">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>
</div>

<style>
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: var(--success);
    color: white;
}

.status-inactive {
    background: var(--danger);
    color: white;
}

.status-pending {
    background: var(--warning);
    color: var(--dark-brown);
}
</style>

<script>
// Password confirmation validation
document.querySelector('input[name="confirm_password"]').addEventListener('input', function() {
    const newPassword = document.querySelector('input[name="new_password"]').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.style.borderColor = '#ff6b6b';
    } else {
        this.setCustomValidity('');
        this.style.borderColor = '';
    }
});

// Form validation feedback
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        submitBtn.disabled = true;
    });
});
</script>

<?php include '../includes/footer.php'; ?>
