<?php
require_once '../config/config.php';

$pageTitle = 'My Orders';
$pageDescription = 'View and track your order history.';

// Redirect if not logged in
if (!is_logged_in()) {
    $_SESSION['redirect_after_login'] = SITE_URL . '/user/orders.php';
    redirect(SITE_URL . '/auth/login.php');
}

$userId = current_user_id();
$db = GeusGalore\Database::getInstance();

// Get filters
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// Build query
$whereClause = "WHERE o.user_id = ?";
$params = [$userId];

if (!empty($status)) {
    $whereClause .= " AND o.status = ?";
    $params[] = $status;
}

// Get orders
$orders = $db->fetchAll("
    SELECT o.*, COUNT(oi.id) as item_count,
           GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as items_summary
    FROM orders o 
    LEFT JOIN order_items oi ON o.id = oi.order_id 
    LEFT JOIN products p ON oi.product_id = p.id
    $whereClause
    GROUP BY o.id 
    ORDER BY o.created_at DESC 
    LIMIT $limit OFFSET $offset
", $params);

$totalOrders = $db->fetch("SELECT COUNT(*) as count FROM orders o $whereClause", $params)['count'];
$totalPages = ceil($totalOrders / $limit);

// Get order stats
$orderStats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM orders WHERE user_id = ?", [$userId])['count'],
    'pending' => $db->fetch("SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = 'pending'", [$userId])['count'],
    'completed' => $db->fetch("SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status IN ('delivered', 'completed')", [$userId])['count'],
    'total_spent' => $db->fetch("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE user_id = ? AND status IN ('delivered', 'completed')", [$userId])['total']
];

include '../includes/header.php';
?>

<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 100px 0 60px; position: relative; overflow: hidden;">
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center; max-width: 800px; margin: 0 auto;">
            <h1 style="font-size: clamp(2.5rem, 6vw, 4rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                <i class="fas fa-shopping-bag"></i> My Orders
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 600px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                Track your purchases and order history
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <!-- Order Stats -->
    <div class="row" style="margin-bottom: 40px;">
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-ocean); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo number_format($orderStats['total']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-shopping-bag"></i> Total Orders
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-warm); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo number_format($orderStats['pending']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-clock"></i> Pending
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-forest); color: white;">
                <div style="font-size: 36px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo number_format($orderStats['completed']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-check-circle"></i> Completed
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="text-align: center; background: var(--gradient-sunset); color: white;">
                <div style="font-size: 28px; font-weight: 800; margin-bottom: 10px;">
                    <?php echo format_currency($orderStats['total_spent']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600;">
                    <i class="fas fa-dollar-sign"></i> Total Spent
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="modern-card" style="margin-bottom: 30px; padding: 25px;">
        <h3 style="margin-bottom: 20px; color: var(--charcoal); font-weight: 700;">
            <i class="fas fa-filter"></i> Filter Orders
        </h3>
        
        <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
            <a href="orders.php" class="btn <?php echo empty($status) ? 'btn-primary' : 'btn-outline'; ?>">
                All Orders
            </a>
            <a href="orders.php?status=pending" class="btn <?php echo $status === 'pending' ? 'btn-primary' : 'btn-outline'; ?>">
                Pending
            </a>
            <a href="orders.php?status=processing" class="btn <?php echo $status === 'processing' ? 'btn-primary' : 'btn-outline'; ?>">
                Processing
            </a>
            <a href="orders.php?status=shipped" class="btn <?php echo $status === 'shipped' ? 'btn-primary' : 'btn-outline'; ?>">
                Shipped
            </a>
            <a href="orders.php?status=delivered" class="btn <?php echo $status === 'delivered' ? 'btn-primary' : 'btn-outline'; ?>">
                Delivered
            </a>
        </div>
    </div>

    <!-- Orders List -->
    <div class="modern-card">
        <div style="padding: 25px 25px 0; border-bottom: 1px solid var(--cream);">
            <h2 style="margin-bottom: 20px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-list"></i> Order History
                <?php if (!empty($status)): ?>
                    <span style="font-size: 16px; color: var(--medium-brown);">(<?php echo ucfirst($status); ?>)</span>
                <?php endif; ?>
            </h2>
        </div>
        
        <?php if (empty($orders)): ?>
            <div style="text-align: center; padding: 60px 20px; color: var(--medium-brown);">
                <i class="fas fa-shopping-bag" style="font-size: 64px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>No orders found</h3>
                <p>You haven't placed any orders yet<?php echo !empty($status) ? ' with this status' : ''; ?>.</p>
                <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary" style="margin-top: 20px;">
                    <i class="fas fa-shopping-cart"></i> Start Shopping
                </a>
            </div>
        <?php else: ?>
            <div style="padding: 0;">
                <?php foreach ($orders as $order): ?>
                    <div style="padding: 25px; border-bottom: 1px solid var(--cream); transition: all 0.3s ease;" 
                         onmouseover="this.style.backgroundColor='var(--cream)'" 
                         onmouseout="this.style.backgroundColor=''">
                        
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; flex-wrap: wrap; gap: 15px;">
                            <div>
                                <h4 style="color: var(--coral); font-weight: 700; margin-bottom: 5px; font-size: 18px;">
                                    Order #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?>
                                </h4>
                                <div style="color: var(--medium-brown); font-size: 14px;">
                                    <i class="fas fa-calendar"></i> 
                                    <?php echo date('M j, Y g:i A', strtotime($order['created_at'])); ?>
                                </div>
                            </div>
                            
                            <div style="text-align: right;">
                                <div style="font-size: 20px; font-weight: 700; color: var(--success); margin-bottom: 5px;">
                                    <?php echo format_currency($order['total_amount']); ?>
                                </div>
                                <span class="status-badge status-<?php echo $order['status']; ?>">
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 5px;">
                                <i class="fas fa-box"></i> Items (<?php echo $order['item_count']; ?>):
                            </div>
                            <div style="color: var(--medium-brown); font-size: 14px; line-height: 1.5;">
                                <?php echo htmlspecialchars($order['items_summary']); ?>
                            </div>
                        </div>
                        
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline" style="font-size: 14px; padding: 8px 16px;">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            
                            <?php if ($order['status'] === 'delivered'): ?>
                                <a href="reorder.php?id=<?php echo $order['id']; ?>" class="btn btn-primary" style="font-size: 14px; padding: 8px 16px;">
                                    <i class="fas fa-redo"></i> Reorder
                                </a>
                            <?php endif; ?>
                            
                            <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                                <button onclick="trackOrder(<?php echo $order['id']; ?>)" class="btn" style="background: var(--warning); color: white; font-size: 14px; padding: 8px 16px;">
                                    <i class="fas fa-truck"></i> Track Order
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 30px; gap: 10px;">
            <?php if ($page > 1): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                   class="btn btn-outline">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            <?php endif; ?>
            
            <span style="color: var(--medium-brown); font-weight: 600; padding: 0 20px;">
                Page <?php echo $page; ?> of <?php echo $totalPages; ?>
            </span>
            
            <?php if ($page < $totalPages): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                   class="btn btn-outline">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <!-- Quick Actions -->
    <div style="text-align: center; margin-top: 40px;">
        <a href="dashboard.php" class="btn btn-outline">
            <i class="fas fa-tachometer-alt"></i> Back to Dashboard
        </a>
        <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary">
            <i class="fas fa-shopping-cart"></i> Continue Shopping
        </a>
    </div>
</div>

<style>
.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: var(--warning);
    color: var(--dark-brown);
}

.status-processing {
    background: var(--info);
    color: white;
}

.status-shipped {
    background: var(--coral);
    color: white;
}

.status-delivered, .status-completed {
    background: var(--success);
    color: white;
}

.status-cancelled {
    background: var(--danger);
    color: white;
}
</style>

<script>
function trackOrder(orderId) {
    alert('Order tracking feature coming soon! Order #' + orderId + ' is being processed.');
}

// Auto-refresh order status every 30 seconds for pending/processing orders
if (document.querySelector('.status-pending, .status-processing')) {
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 30000);
}
</script>

<?php include '../includes/footer.php'; ?>
