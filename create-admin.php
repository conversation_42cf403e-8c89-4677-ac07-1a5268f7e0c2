<?php
require_once 'config/config.php';

// This script creates a new admin user
// Run this once, then delete this file for security

$username = 'admin';
$email = '<EMAIL>';
$password = 'admin123';
$role = 'super_admin';

try {
    $db = GeusGalore\Database::getInstance();
    
    // Check if admin_users table exists
    $tableExists = $db->fetch("SHOW TABLES LIKE 'admin_users'");
    
    if (!$tableExists) {
        echo "❌ Error: admin_users table doesn't exist. Please import database/schema.sql first.\n";
        exit;
    }
    
    // Check if admin user already exists
    $existingAdmin = $db->fetch("SELECT * FROM admin_users WHERE username = ? OR email = ?", [$username, $email]);
    
    if ($existingAdmin) {
        echo "⚠️ Admin user already exists!\n";
        echo "Username: " . $existingAdmin['username'] . "\n";
        echo "Email: " . $existingAdmin['email'] . "\n";
        echo "Try logging in with: username='admin', password='admin123'\n";
        echo "If that doesn't work, delete the existing admin and run this script again.\n";
        exit;
    }
    
    // Create password hash
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert new admin user
    $result = $db->execute(
        "INSERT INTO admin_users (username, email, password, role) VALUES (?, ?, ?, ?)",
        [$username, $email, $hashedPassword, $role]
    );
    
    if ($result) {
        echo "✅ SUCCESS! Admin user created successfully!\n\n";
        echo "🔐 Login Details:\n";
        echo "URL: " . SITE_URL . "/admin/login.php\n";
        echo "Username: admin\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n\n";
        echo "⚠️ IMPORTANT: Delete this file (create-admin.php) after use for security!\n";
    } else {
        echo "❌ Error: Failed to create admin user.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
    echo "\nPossible issues:\n";
    echo "1. Database connection failed - check config/config.php\n";
    echo "2. Database schema not imported - import database/schema.sql\n";
    echo "3. Database permissions issue\n";
}
?>
