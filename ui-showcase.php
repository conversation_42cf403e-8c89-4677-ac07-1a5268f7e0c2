<?php
require_once 'config/config.php';
$pageTitle = 'UI Showcase - Modern Design';
include 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <h1>🎨 Modern UI Showcase</h1>
        <p>Experience the beautiful, responsive design of Geu's Galore</p>
    </div>
</div>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <!-- Navigation Test -->
    <div class="modern-card" style="margin-bottom: 40px;">
        <h2 style="background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; margin-bottom: 25px;">
            🧭 Navigation & Pages
        </h2>
        <div class="row">
            <div class="col col-4">
                <h3 style="color: var(--charcoal); margin-bottom: 15px;">🏠 Main Pages</h3>
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <a href="<?php echo SITE_URL; ?>/" class="btn btn-primary" style="justify-content: flex-start;">
                        <i class="fas fa-home"></i> Homepage
                    </a>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-secondary" style="justify-content: flex-start;">
                        <i class="fas fa-shopping-bag"></i> Products
                    </a>
                    <a href="<?php echo SITE_URL; ?>/pages/about.php" class="btn btn-outline" style="justify-content: flex-start;">
                        <i class="fas fa-info-circle"></i> About Us
                    </a>
                </div>
            </div>
            <div class="col col-4">
                <h3 style="color: var(--charcoal); margin-bottom: 15px;">🔐 Authentication</h3>
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <a href="<?php echo SITE_URL; ?>/auth/login.php" class="btn btn-info" style="justify-content: flex-start;">
                        <i class="fas fa-sign-in-alt"></i> Login Page
                    </a>
                    <a href="<?php echo SITE_URL; ?>/auth/register.php" class="btn btn-success" style="justify-content: flex-start;">
                        <i class="fas fa-user-plus"></i> Register Page
                    </a>
                    <?php if (is_logged_in()): ?>
                        <a href="<?php echo SITE_URL; ?>/user/dashboard.php" class="btn btn-primary" style="justify-content: flex-start;">
                            <i class="fas fa-tachometer-alt"></i> User Dashboard
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col col-4">
                <h3 style="color: var(--charcoal); margin-bottom: 15px;">⚙️ Admin</h3>
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <a href="<?php echo SITE_URL; ?>/admin/login.php" class="btn" style="background: var(--gradient-warm); color: white; justify-content: flex-start;">
                        <i class="fas fa-user-shield"></i> Admin Login
                    </a>
                    <?php if (is_admin()): ?>
                        <a href="<?php echo SITE_URL; ?>/admin/index.php" class="btn" style="background: var(--gradient-forest); color: white; justify-content: flex-start;">
                            <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Button Showcase -->
    <div class="modern-card" style="margin-bottom: 40px;">
        <h2 style="background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; margin-bottom: 25px;">
            🔘 Button Styles
        </h2>
        <div class="row">
            <div class="col col-6">
                <h3 style="color: var(--charcoal); margin-bottom: 15px;">Standard Buttons</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px;">
                    <button class="btn btn-primary">Primary</button>
                    <button class="btn btn-secondary">Secondary</button>
                    <button class="btn btn-outline">Outline</button>
                    <button class="btn btn-success">Success</button>
                    <button class="btn btn-info">Info</button>
                </div>
            </div>
            <div class="col col-6">
                <h3 style="color: var(--charcoal); margin-bottom: 15px;">Icon Buttons</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px;">
                    <button class="btn btn-primary"><i class="fas fa-heart"></i> Like</button>
                    <button class="btn btn-secondary"><i class="fas fa-share"></i> Share</button>
                    <button class="btn btn-outline"><i class="fas fa-download"></i> Download</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Elements -->
    <div class="modern-card" style="margin-bottom: 40px;">
        <h2 style="background: var(--gradient-forest); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; margin-bottom: 25px;">
            📝 Form Elements
        </h2>
        <div class="row">
            <div class="col col-6">
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-user"></i> Name</label>
                    <div style="position: relative;">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control has-icon" placeholder="Enter your name">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-envelope"></i> Email</label>
                    <div style="position: relative;">
                        <i class="fas fa-envelope input-icon"></i>
                        <input type="email" class="form-control has-icon" placeholder="Enter your email">
                    </div>
                </div>
            </div>
            <div class="col col-6">
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-list"></i> Category</label>
                    <select class="form-control">
                        <option>Select a category</option>
                        <option>Electronics</option>
                        <option>Clothing</option>
                        <option>Home & Garden</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-comment"></i> Message</label>
                    <textarea class="form-control" rows="3" placeholder="Enter your message"></textarea>
                </div>
            </div>
        </div>
    </div>

    <!-- Cards Showcase -->
    <div class="modern-card" style="margin-bottom: 40px;">
        <h2 style="background: var(--gradient-warm); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; margin-bottom: 25px;">
            🃏 Card Layouts
        </h2>
        <div class="row">
            <div class="col col-4">
                <div class="card">
                    <div style="height: 200px; background: var(--gradient-sunset); display: flex; align-items: center; justify-content: center; color: white; font-size: 48px;">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title">Product Card</h3>
                        <p class="card-text">Beautiful product card with gradient background and hover effects.</p>
                        <div class="card-price">PGK 99.99</div>
                        <button class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                    </div>
                </div>
            </div>
            <div class="col col-4">
                <div class="modern-card" style="text-align: center; background: var(--gradient-ocean); color: white;">
                    <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-star" style="font-size: 35px;"></i>
                    </div>
                    <h3 style="margin-bottom: 15px;">Feature Card</h3>
                    <p>Modern card with gradient background and icon.</p>
                </div>
            </div>
            <div class="col col-4">
                <div class="modern-card">
                    <h3 style="margin-bottom: 15px; color: var(--charcoal);">Info Card</h3>
                    <p style="color: var(--medium-brown); margin-bottom: 20px;">
                        Clean and modern information card with subtle styling.
                    </p>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-outline" style="flex: 1;">Learn More</button>
                        <button class="btn btn-primary" style="flex: 1;">Get Started</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Color Palette -->
    <div class="modern-card" style="margin-bottom: 40px;">
        <h2 style="background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; margin-bottom: 25px;">
            🌈 Papua New Guinea Color Palette
        </h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="text-align: center;">
                <div style="height: 100px; background: var(--coral); border-radius: var(--radius-lg); margin-bottom: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Coral
                </div>
                <p style="color: var(--coral); font-weight: 600;">#FF6B6B</p>
            </div>
            <div style="text-align: center;">
                <div style="height: 100px; background: var(--sunset-orange); border-radius: var(--radius-lg); margin-bottom: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Sunset Orange
                </div>
                <p style="color: var(--sunset-orange); font-weight: 600;">#FF8E53</p>
            </div>
            <div style="text-align: center;">
                <div style="height: 100px; background: var(--emerald); border-radius: var(--radius-lg); margin-bottom: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Emerald
                </div>
                <p style="color: var(--emerald); font-weight: 600;">#6BCF7F</p>
            </div>
            <div style="text-align: center;">
                <div style="height: 100px; background: var(--ocean-blue); border-radius: var(--radius-lg); margin-bottom: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; box-shadow: var(--shadow-md);">
                    Ocean Blue
                </div>
                <p style="color: var(--ocean-blue); font-weight: 600;">#4ECDC4</p>
            </div>
        </div>
    </div>

    <!-- Mobile Responsiveness Info -->
    <div class="modern-card" style="background: var(--gradient-forest); color: white; text-align: center;">
        <h2 style="margin-bottom: 20px;">📱 Mobile Responsive Design</h2>
        <p style="margin-bottom: 30px; font-size: 18px;">
            This design is fully responsive and optimized for all devices
        </p>
        <div class="row">
            <div class="col col-4">
                <i class="fas fa-mobile-alt" style="font-size: 48px; margin-bottom: 15px;"></i>
                <h3>Mobile First</h3>
                <p>Designed for mobile devices with touch-friendly interfaces</p>
            </div>
            <div class="col col-4">
                <i class="fas fa-tablet-alt" style="font-size: 48px; margin-bottom: 15px;"></i>
                <h3>Tablet Ready</h3>
                <p>Perfect layout and spacing for tablet devices</p>
            </div>
            <div class="col col-4">
                <i class="fas fa-desktop" style="font-size: 48px; margin-bottom: 15px;"></i>
                <h3>Desktop Optimized</h3>
                <p>Beautiful and functional on large screens</p>
            </div>
        </div>
        <div style="margin-top: 30px;">
            <p style="font-size: 14px; opacity: 0.9;">
                🔧 Resize your browser window to test responsiveness
            </p>
        </div>
    </div>
</div>

<script>
console.log('🎨 UI Showcase loaded successfully!');
console.log('✅ All modern UI components are working');

// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Add click effects to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!this.classList.contains('clicked')) {
                this.classList.add('clicked');
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 200);
            }
        });
    });
    
    // Add focus effects to form inputs
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>

<style>
.btn.clicked {
    transform: scale(0.95) !important;
}

@media (max-width: 768px) {
    .col-4, .col-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
