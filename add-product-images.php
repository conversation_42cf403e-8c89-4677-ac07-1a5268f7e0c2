<?php
require_once 'config/config.php';

echo "<h1>📸 Product Images Manager</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; border: none; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
    .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f8f9fa; }
    .product-card img { width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 10px; }
    .image-url { font-size: 12px; color: #666; word-break: break-all; margin-top: 5px; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    
    // Check if we should add images
    $addImages = isset($_GET['add']) && $_GET['add'] === 'yes';
    
    // High-quality stock images for PNG products
    $productImages = [
        'Premium Coffee Beans - Papua New Guinea' => [
            'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=800&h=600&fit=crop&crop=center'
        ],
        'Traditional PNG Handicraft Mask' => [
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&crop=center'
        ],
        'Organic Coconut Oil - Cold Pressed' => [
            'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=800&h=600&fit=crop&crop=center'
        ],
        'PNG Sea Salt - Natural Harvest' => [
            'https://images.unsplash.com/photo-1518843875459-f738682238a6?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center'
        ],
        'Handwoven Traditional Bilum Bag' => [
            'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800&h=600&fit=crop&crop=center'
        ],
        'PNG Vanilla Extract - Pure' => [
            'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center'
        ],
        'Traditional PNG Drums - Kundu' => [
            'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&crop=center'
        ],
        'PNG Honey - Wild Forest' => [
            'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1558642452-9d2a7deb7f62?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center'
        ],
        'PNG Spice Mix - Traditional Blend' => [
            'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center'
        ],
        'PNG Shell Jewelry Set' => [
            'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center'
        ],
        'PNG Cocoa Powder - Premium Grade' => [
            'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center'
        ],
        'Traditional PNG Tapa Cloth' => [
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=800&h=600&fit=crop&crop=center',
            'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=600&fit=crop&crop=center'
        ]
    ];
    
    if ($addImages) {
        echo "<h2>📸 Adding Product Images</h2>";
        
        // Create uploads directory if it doesn't exist
        $uploadsDir = ROOT_PATH . '/uploads/products/';
        if (!file_exists($uploadsDir)) {
            mkdir($uploadsDir, 0755, true);
            echo "<div class='success'>✅ Created uploads/products directory</div>";
        }
        
        // Get all products
        $products = $db->fetchAll("SELECT * FROM products ORDER BY id");
        
        $imageCount = 0;
        $errorCount = 0;
        
        foreach ($products as $product) {
            echo "<h3>🛍️ Processing: {$product['name']}</h3>";
            
            // Check if product already has images
            $existingImages = $db->fetchAll("SELECT * FROM product_images WHERE product_id = ?", [$product['id']]);
            
            if (!empty($existingImages)) {
                echo "<div class='info'>ℹ️ Product already has " . count($existingImages) . " images</div>";
                continue;
            }
            
            // Get images for this product
            $images = $productImages[$product['name']] ?? [];
            
            if (empty($images)) {
                echo "<div class='warning'>⚠️ No images defined for this product</div>";
                continue;
            }
            
            foreach ($images as $index => $imageUrl) {
                try {
                    // Generate filename
                    $extension = 'jpg';
                    $filename = $product['slug'] . '-' . ($index + 1) . '.' . $extension;
                    $filepath = $uploadsDir . $filename;
                    
                    // Download image
                    $imageData = file_get_contents($imageUrl);
                    
                    if ($imageData === false) {
                        echo "<div class='error'>❌ Failed to download image from: $imageUrl</div>";
                        $errorCount++;
                        continue;
                    }
                    
                    // Save image
                    if (file_put_contents($filepath, $imageData)) {
                        // Add to database
                        $imageId = $db->insert('product_images', [
                            'product_id' => $product['id'],
                            'image_path' => $filename,
                            'alt_text' => $product['name'],
                            'is_primary' => $index === 0 ? 1 : 0,
                            'sort_order' => $index + 1
                        ]);
                        
                        if ($imageId) {
                            echo "<div class='success'>✅ Added image: $filename</div>";
                            $imageCount++;
                        } else {
                            echo "<div class='error'>❌ Failed to save image to database: $filename</div>";
                            $errorCount++;
                        }
                    } else {
                        echo "<div class='error'>❌ Failed to save image file: $filename</div>";
                        $errorCount++;
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Error processing image: " . $e->getMessage() . "</div>";
                    $errorCount++;
                }
            }
        }
        
        echo "<h2>📊 Image Addition Summary</h2>";
        echo "<div class='success'>✅ Successfully added: $imageCount images</div>";
        if ($errorCount > 0) {
            echo "<div class='error'>❌ Failed to add: $errorCount images</div>";
        }
        
        echo "<h3>🚀 Your Store is Ready!</h3>";
        echo "<ul>";
        echo "<li><a href='shop/products.php' class='btn'>🛍️ View Products with Images</a></li>";
        echo "<li><a href='admin/products.php' class='btn'>⚙️ Manage Products (Admin)</a></li>";
        echo "<li><a href='admin/index.php' class='btn'>📊 Admin Dashboard</a></li>";
        echo "</ul>";
        
    } else {
        echo "<h2>📸 Product Images Preview</h2>";
        echo "<p>This will add high-quality stock images to your products. Here's a preview:</p>";
        
        // Get products without images
        $products = $db->fetchAll("
            SELECT p.*, 
                   (SELECT COUNT(*) FROM product_images WHERE product_id = p.id) as image_count
            FROM products p 
            ORDER BY p.id
        ");
        
        $productsNeedingImages = array_filter($products, function($p) { return $p['image_count'] == 0; });
        
        echo "<div class='info'>Products needing images: " . count($productsNeedingImages) . " out of " . count($products) . "</div>";
        
        if (empty($productsNeedingImages)) {
            echo "<div class='success'>✅ All products already have images!</div>";
            echo "<p><a href='shop/products.php' class='btn'>🛍️ View Your Products</a></p>";
        } else {
            echo "<div class='product-grid'>";
            
            foreach ($productsNeedingImages as $product) {
                $images = $productImages[$product['name']] ?? [];
                
                echo "<div class='product-card'>";
                echo "<h4>{$product['name']}</h4>";
                
                if (!empty($images)) {
                    echo "<img src='{$images[0]}' alt='{$product['name']}' loading='lazy'>";
                    echo "<div class='info'>✅ " . count($images) . " images ready</div>";
                    echo "<div class='image-url'>Primary: {$images[0]}</div>";
                } else {
                    echo "<div style='height: 200px; background: #ddd; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-bottom: 10px;'>";
                    echo "<span style='color: #666;'>No images available</span>";
                    echo "</div>";
                    echo "<div class='warning'>⚠️ No images defined</div>";
                }
                
                echo "<div style='margin-top: 10px;'>";
                echo "<strong>Price:</strong> PGK " . number_format($product['price'], 2);
                if ($product['sale_price']) {
                    echo " <span style='color: #dc3545;'>(Sale: PGK " . number_format($product['sale_price'], 2) . ")</span>";
                }
                echo "</div>";
                echo "</div>";
            }
            
            echo "</div>";
            
            echo "<div class='warning'>";
            echo "<strong>📝 Note:</strong> Images will be downloaded from Unsplash (high-quality stock photos) and saved to your uploads/products/ directory.";
            echo "</div>";
            
            echo "<p><a href='?add=yes' class='btn btn-success'>📸 Add Images to All Products</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    
    echo "<h3>🔧 Manual Image Addition:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='admin/products.php'>Admin Products</a></li>";
    echo "<li>Click 'Edit' on any product</li>";
    echo "<li>Upload images using the product form</li>";
    echo "<li>Set one image as primary</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file after use!<br>";
echo "📸 <strong>Image Credits:</strong> All images are from Unsplash.com (free stock photos)";
echo "</p>";

echo "</div>";
?>
