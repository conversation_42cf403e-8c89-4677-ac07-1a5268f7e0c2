<?php
require_once 'config/config.php';

$pageTitle = 'Shopping Cart';
$pageDescription = 'Review your selected items and proceed to checkout.';

$cart = new GeusGalore\Cart();
$userId = current_user_id();

// Handle cart updates
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'update_cart') {
            foreach ($_POST['quantities'] as $cartId => $quantity) {
                $result = $cart->updateCartItem($cartId, intval($quantity), $userId);
                if (!$result['success']) {
                    $message = $result['error'];
                    $messageType = 'error';
                    break;
                }
            }
            if (empty($message)) {
                $message = 'Cart updated successfully!';
                $messageType = 'success';
            }
        }
        
        elseif ($_POST['action'] === 'remove_item') {
            $cartId = intval($_POST['cart_id']);
            $result = $cart->removeFromCart($cartId, $userId);
            if ($result['success']) {
                $message = 'Item removed from cart!';
                $messageType = 'success';
            } else {
                $message = 'Failed to remove item.';
                $messageType = 'error';
            }
        }
        
        elseif ($_POST['action'] === 'clear_cart') {
            $result = $cart->clearCart($userId);
            if ($result['success']) {
                $message = 'Cart cleared successfully!';
                $messageType = 'success';
            }
        }
    }
}

// Get cart data
$cartData = $cart->getCartTotal($userId);
$stockErrors = $cart->validateCartStock($userId);

include 'includes/header.php';
?>

<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 50%, #ff8e53 100%); padding: 100px 0 60px; position: relative; overflow: hidden;">
    <!-- Animated Background Elements -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1;">
        <div style="position: absolute; top: 20%; left: 10%; width: 100px; height: 100px; background: white; border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div style="position: absolute; top: 60%; right: 15%; width: 150px; height: 150px; background: white; border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; bottom: 20%; left: 20%; width: 80px; height: 80px; background: white; border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>
    
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center; max-width: 800px; margin: 0 auto;">
            <h1 style="font-size: clamp(2.5rem, 6vw, 4rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                <i class="fas fa-shopping-cart"></i> Shopping Cart
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 600px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                Review your items and proceed to checkout
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    
    <?php if ($message): ?>
        <div style="background: <?php echo $messageType === 'success' ? 'var(--success)' : 'var(--danger)'; ?>; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 30px; text-align: center;">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($stockErrors)): ?>
        <div style="background: var(--warning); color: var(--dark-brown); padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h4 style="margin-bottom: 15px;"><i class="fas fa-exclamation-triangle"></i> Stock Issues</h4>
            <?php foreach ($stockErrors as $error): ?>
                <div style="margin-bottom: 10px;">
                    <strong><?php echo htmlspecialchars($error['product_name']); ?>:</strong>
                    Only <?php echo $error['available']; ?> available (you requested <?php echo $error['requested']; ?>)
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <?php if (empty($cartData['items'])): ?>
        <!-- Empty Cart -->
        <div class="modern-card" style="text-align: center; padding: 60px 20px;">
            <i class="fas fa-shopping-cart" style="font-size: 80px; color: var(--medium-brown); opacity: 0.5; margin-bottom: 30px;"></i>
            <h2 style="color: var(--charcoal); margin-bottom: 20px; font-weight: 700;">Your cart is empty</h2>
            <p style="color: var(--medium-brown); margin-bottom: 30px; font-size: 18px;">
                Looks like you haven't added any items to your cart yet.
            </p>
            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary" style="font-size: 18px; padding: 15px 30px;">
                <i class="fas fa-shopping-bag"></i> Start Shopping
            </a>
        </div>
    <?php else: ?>
        <form method="POST">
            <input type="hidden" name="action" value="update_cart">
            
            <div class="row">
                <!-- Cart Items -->
                <div class="col col-8">
                    <div class="modern-card">
                        <div style="padding: 25px 25px 0; border-bottom: 1px solid var(--cream);">
                            <h2 style="margin-bottom: 20px; background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                                <i class="fas fa-list"></i> Cart Items (<?php echo count($cartData['items']); ?>)
                            </h2>
                        </div>
                        
                        <div style="padding: 0;">
                            <?php foreach ($cartData['items'] as $item): ?>
                                <div class="cart-item" style="padding: 25px; border-bottom: 1px solid var(--cream); transition: all 0.3s ease;" 
                                     onmouseover="this.style.backgroundColor='var(--cream)'" 
                                     onmouseout="this.style.backgroundColor=''">
                                    
                                    <div style="display: flex; gap: 20px; align-items: center;">
                                        <!-- Product Image -->
                                        <div style="flex-shrink: 0;">
                                            <?php if ($item['image']): ?>
                                                <img src="<?php echo UPLOAD_URL . 'products/' . $item['image']; ?>" 
                                                     alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                     style="width: 80px; height: 80px; object-fit: cover; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                            <?php else: ?>
                                                <div style="width: 80px; height: 80px; background: var(--cream); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                                    <i class="fas fa-image" style="color: var(--medium-brown); font-size: 24px;"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Product Info -->
                                        <div style="flex: 1;">
                                            <h4 style="color: var(--charcoal); font-weight: 700; margin-bottom: 8px; font-size: 18px;">
                                                <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $item['slug']; ?>" 
                                                   style="color: inherit; text-decoration: none;">
                                                    <?php echo htmlspecialchars($item['name']); ?>
                                                </a>
                                            </h4>
                                            
                                            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                                                <div style="font-size: 20px; font-weight: 700; color: var(--coral);">
                                                    <?php echo format_currency($item['sale_price'] ?: $item['price']); ?>
                                                </div>
                                                
                                                <?php if ($item['sale_price']): ?>
                                                    <div style="font-size: 16px; color: var(--medium-brown); text-decoration: line-through;">
                                                        <?php echo format_currency($item['price']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <!-- Stock Status -->
                                            <?php if ($item['manage_stock']): ?>
                                                <div style="font-size: 12px; color: <?php echo $item['stock_quantity'] < 10 ? 'var(--danger)' : 'var(--success)'; ?>; margin-bottom: 10px;">
                                                    <i class="fas fa-box"></i> 
                                                    <?php echo $item['stock_quantity']; ?> in stock
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Quantity & Actions -->
                                        <div style="display: flex; flex-direction: column; align-items: center; gap: 15px;">
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <label style="font-weight: 600; color: var(--charcoal);">Qty:</label>
                                                <input type="number" 
                                                       name="quantities[<?php echo $item['id']; ?>]" 
                                                       value="<?php echo $item['quantity']; ?>" 
                                                       min="1" 
                                                       max="<?php echo $item['manage_stock'] ? $item['stock_quantity'] : 999; ?>"
                                                       style="width: 70px; padding: 8px; border: 2px solid var(--cream); border-radius: 8px; text-align: center; font-weight: 600;">
                                            </div>
                                            
                                            <div style="font-size: 18px; font-weight: 700; color: var(--success);">
                                                <?php echo format_currency(($item['sale_price'] ?: $item['price']) * $item['quantity']); ?>
                                            </div>
                                            
                                            <button type="submit" name="action" value="remove_item" 
                                                    onclick="this.form.querySelector('input[name=cart_id]').value='<?php echo $item['id']; ?>'"
                                                    class="btn" style="background: var(--danger); color: white; font-size: 12px; padding: 6px 12px;">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Cart Actions -->
                        <div style="padding: 25px; background: var(--cream); display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync"></i> Update Cart
                            </button>
                            
                            <button type="submit" name="action" value="clear_cart" 
                                    onclick="return confirm('Are you sure you want to clear your cart?')"
                                    class="btn" style="background: var(--danger); color: white;">
                                <i class="fas fa-trash"></i> Clear Cart
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Cart Summary -->
                <div class="col col-4">
                    <div class="modern-card" style="position: sticky; top: 20px;">
                        <h3 style="margin-bottom: 25px; background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-calculator"></i> Order Summary
                        </h3>
                        
                        <div style="display: flex; flex-direction: column; gap: 15px; margin-bottom: 25px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                                <span style="font-weight: 600;">Subtotal:</span>
                                <span style="font-weight: 700; color: var(--coral); font-size: 18px;">
                                    <?php echo format_currency($cartData['subtotal']); ?>
                                </span>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                                <span style="font-weight: 600;">Items:</span>
                                <span style="font-weight: 600; color: var(--charcoal);">
                                    <?php echo $cartData['item_count']; ?>
                                </span>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: var(--cream); border-radius: 8px;">
                                <span style="font-weight: 600;">Shipping:</span>
                                <span style="font-weight: 600; color: var(--success);">
                                    FREE
                                </span>
                            </div>
                            
                            <hr style="border: none; border-top: 2px solid var(--cream); margin: 10px 0;">
                            
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: var(--gradient-sunset); color: white; border-radius: 10px;">
                                <span style="font-weight: 700; font-size: 18px;">Total:</span>
                                <span style="font-weight: 900; font-size: 24px;">
                                    <?php echo format_currency($cartData['total']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Checkout Actions -->
                        <div style="display: flex; flex-direction: column; gap: 15px;">
                            <?php if (empty($stockErrors)): ?>
                                <a href="<?php echo SITE_URL; ?>/checkout.php" class="btn btn-primary" style="width: 100%; font-size: 18px; padding: 15px; text-align: center;">
                                    <i class="fas fa-credit-card"></i> Proceed to Checkout
                                </a>
                            <?php else: ?>
                                <div class="btn" style="background: var(--medium-brown); color: white; width: 100%; font-size: 18px; padding: 15px; text-align: center; cursor: not-allowed;">
                                    <i class="fas fa-exclamation-triangle"></i> Fix Stock Issues First
                                </div>
                            <?php endif; ?>
                            
                            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-outline" style="width: 100%; text-align: center;">
                                <i class="fas fa-shopping-bag"></i> Continue Shopping
                            </a>
                        </div>
                        
                        <!-- Security Badge -->
                        <div style="text-align: center; margin-top: 25px; padding: 15px; background: rgba(0,0,0,0.05); border-radius: 8px;">
                            <div style="color: var(--success); margin-bottom: 8px;">
                                <i class="fas fa-shield-alt" style="font-size: 24px;"></i>
                            </div>
                            <div style="font-size: 12px; color: var(--medium-brown); font-weight: 600;">
                                Secure Checkout<br>
                                SSL Encrypted
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <input type="hidden" name="cart_id" value="">
        </form>
    <?php endif; ?>
</div>

<style>
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.cart-item:last-child {
    border-bottom: none !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .cart-item > div {
        flex-direction: column !important;
        text-align: center !important;
    }
    
    .cart-item img {
        width: 60px !important;
        height: 60px !important;
    }
}
</style>

<script>
// Auto-update cart when quantity changes
document.querySelectorAll('input[type="number"]').forEach(input => {
    input.addEventListener('change', function() {
        // Auto-submit form when quantity changes
        setTimeout(() => {
            this.form.submit();
        }, 500);
    });
});

// Smooth animations
document.addEventListener('DOMContentLoaded', function() {
    const cartItems = document.querySelectorAll('.cart-item');
    cartItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.6s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
