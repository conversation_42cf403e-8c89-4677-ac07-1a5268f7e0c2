<?php
require_once 'config/config.php';

echo "<h1>🔧 Admin Login Test & Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .btn { display: inline-block; padding: 12px 24px; margin: 10px 5px; text-decoration: none; border-radius: 5px; font-weight: bold; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .credentials { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff; }
    code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
</style>";

echo "<div class='container'>";

echo "<h2>🔍 Current Configuration Analysis</h2>";

// Show current SITE_URL
echo "<p><strong>Current SITE_URL:</strong> <code>" . SITE_URL . "</code></p>";
echo "<p><strong>Current Script:</strong> <code>" . $_SERVER['SCRIPT_NAME'] . "</code></p>";
echo "<p><strong>Document Root:</strong> <code>" . $_SERVER['DOCUMENT_ROOT'] . "</code></p>";
echo "<p><strong>HTTP Host:</strong> <code>" . $_SERVER['HTTP_HOST'] . "</code></p>";

// Check if admin login works
echo "<h2>🔐 Admin Login Status</h2>";

try {
    $db = GeusGalore\Database::getInstance();
    
    // Check admin user
    $admin = $db->fetch("SELECT * FROM admin_users WHERE username = 'admin'");
    
    if ($admin) {
        echo "<p class='success'>✅ Admin user exists in database</p>";
        
        // Test password
        if (password_verify('admin123', $admin['password'])) {
            echo "<p class='success'>✅ Password verification works</p>";
            echo "<p class='info'>🎉 Admin login should work now!</p>";
        } else {
            echo "<p class='error'>❌ Password verification failed</p>";
            echo "<p class='info'>🔧 Fixing password...</p>";
            
            // Fix password
            $newHash = password_hash('admin123', PASSWORD_DEFAULT);
            $pdo = $db->getConnection();
            $stmt = $pdo->prepare("UPDATE admin_users SET password = ? WHERE username = 'admin'");
            $result = $stmt->execute([$newHash]);
            
            if ($result) {
                echo "<p class='success'>✅ Password fixed!</p>";
            } else {
                echo "<p class='error'>❌ Failed to fix password</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ No admin user found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🚀 Fixed Admin Login URLs</h2>";
echo "<p class='info'>I've fixed the redirect issues in the admin login system. The double '/admin/admin/' problem should be resolved.</p>";

echo "<div class='warning'>";
echo "<strong>⚠️ URL Configuration Issue Detected:</strong><br>";
echo "The SITE_URL auto-detection might be causing issues. For a permanent fix, consider setting a fixed SITE_URL in config.php:";
echo "<br><br>";
echo "<code>define('SITE_URL', 'https://gaugalore.waghitech.com');</code>";
echo "</div>";

echo "<div class='credentials'>";
echo "<h3>🔐 Admin Login Information:</h3>";
echo "<p><strong>Login URL:</strong> <a href='admin/login.php' target='_blank'>admin/login.php</a></p>";
echo "<p><strong>Username:</strong> admin</p>";
echo "<p><strong>Alternative:</strong> <EMAIL></p>";
echo "<p><strong>Password:</strong> admin123</p>";
echo "</div>";

echo "<h2>🧪 Test Links</h2>";
echo "<p>Test these links to verify everything works:</p>";
echo "<a href='admin/login.php' class='btn btn-primary' target='_blank'>🔐 Test Admin Login</a>";
echo "<a href='admin/index.php' class='btn btn-success' target='_blank'>📊 Test Admin Dashboard</a>";
echo "<a href='index.php' class='btn btn-success'>🏠 Back to Homepage</a>";

echo "<h2>🔧 What I Fixed:</h2>";
echo "<ul>";
echo "<li>✅ Fixed admin login redirect from <code>SITE_URL . '/admin/index.php'</code> to <code>'index.php'</code></li>";
echo "<li>✅ Fixed admin dashboard redirect from <code>SITE_URL . '/admin/login.php'</code> to <code>'login.php'</code></li>";
echo "<li>✅ Updated admin password hash if needed</li>";
echo "<li>✅ Used relative paths to avoid double '/admin/' in URLs</li>";
echo "</ul>";

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file (test-admin-login.php) after testing!";
echo "</p>";

echo "</div>";
?>
