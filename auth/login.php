<?php
require_once '../config/config.php';

$pageTitle = 'Login';
$pageDescription = 'Sign in to your Geu\'s Galore account to access your dashboard and continue shopping.';

// Redirect if already logged in
if (is_logged_in()) {
    redirect(SITE_URL . '/user/dashboard.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error = 'Please enter both email and password.';
    } else {
        $user = new GeusGalore\User();
        $result = $user->login($email, $password);
        
        if ($result['success']) {
            // Transfer session cart to user
            $cart = new GeusGalore\Cart();
            $cart->transferSessionCart($result['user']['id']);
            
            flash_message('success', 'Welcome back!');
            
            // Redirect to intended page or dashboard
            $redirectUrl = $_SESSION['redirect_after_login'] ?? SITE_URL . '/user/dashboard.php';
            unset($_SESSION['redirect_after_login']);
            redirect($redirectUrl);
        } else {
            $error = $result['error'];
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">

<!-- Auth Header -->
<header style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 15px 0; position: fixed; top: 0; left: 0; right: 0; z-index: 1000; backdrop-filter: blur(10px);">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center;">
        <a href="<?php echo SITE_URL; ?>/" style="font-size: 28px; font-weight: 900; color: #ffffff; text-decoration: none; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
            Geu's Galore
        </a>
        <div style="display: flex; gap: 20px; align-items: center;">
            <a href="<?php echo SITE_URL; ?>/" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-home"></i> Home
            </a>
            <a href="<?php echo SITE_URL; ?>/shop/products.php" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-shopping-bag"></i> Shop
            </a>
            <a href="<?php echo SITE_URL; ?>/contact.php" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-envelope"></i> Contact
            </a>
        </div>
    </div>
</header>

<!-- Stunning Login Page -->
<div style="min-height: 100vh; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%); position: relative; display: flex; align-items: center; overflow: hidden; padding-top: 80px;">
    <!-- Animated Background -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
        <div style="position: absolute; top: 20%; left: 10%; width: 300px; height: 300px; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); border-radius: 50%; animation: floatSlow 15s ease-in-out infinite;"></div>
        <div style="position: absolute; bottom: 20%; right: 10%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%); border-radius: 50%; animation: floatSlow 12s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; top: 50%; left: 50%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,255,255,0.06) 0%, transparent 70%); border-radius: 50%; animation: floatSlow 18s ease-in-out infinite;"></div>
    </div>

    <!-- Floating Elements -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; pointer-events: none;">
        <div style="position: absolute; top: 15%; left: 8%; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; animation: morphing 8s ease-in-out infinite;"></div>
        <div style="position: absolute; bottom: 25%; right: 12%; width: 60px; height: 60px; background: rgba(255,255,255,0.08); border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%; animation: morphing 6s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; top: 60%; left: 15%; width: 40px; height: 40px; background: rgba(255,255,255,0.06); border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%; animation: morphing 10s ease-in-out infinite;"></div>
    </div>

    <div class="container" style="max-width: 500px; margin: 0 auto; padding: 20px; position: relative; z-index: 10;">
        <div style="background: rgba(255,255,255,0.15); backdrop-filter: blur(25px); border-radius: 30px; padding: 50px 40px; border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 25px 50px rgba(0,0,0,0.2);"
            <!-- Header Section -->
            <div style="text-align: center; margin-bottom: 40px;">
                <div style="display: inline-block; background: rgba(255,255,255,0.2); backdrop-filter: blur(10px); padding: 12px 24px; border-radius: 50px; margin-bottom: 25px; border: 1px solid rgba(255,255,255,0.3);">
                    <span style="color: white; font-weight: 600; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">
                        🏝️ Papua New Guinea's #1 Store
                    </span>
                </div>

                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1)); border-radius: 30px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; box-shadow: 0 15px 35px rgba(0,0,0,0.2); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-user-circle" style="font-size: 45px; color: white;"></i>
                </div>

                <h1 style="color: white; font-size: 36px; font-weight: 900; margin-bottom: 15px; text-shadow: 2px 2px 20px rgba(0,0,0,0.3);">
                    Welcome Back!
                </h1>
                <p style="color: rgba(255,255,255,0.9); font-size: 18px; font-weight: 500;">
                    Sign in to continue your shopping journey
                </p>
            </div>

            <!-- Login Form -->
            <form method="POST" data-validate>
                <div style="margin-bottom: 25px;">
                    <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                        <i class="fas fa-envelope"></i> Email Address
                    </label>
                    <div style="position: relative;">
                        <input type="email" id="email" name="email"
                               style="width: calc(100% - 4px); padding: 18px 20px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                               placeholder="Enter your email address"
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               required autofocus
                               onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                               onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                        <i class="fas fa-envelope" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                    </div>
                </div>

                <div style="margin-bottom: 25px;">
                    <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <div style="position: relative;">
                        <input type="password" id="password" name="password"
                               style="width: calc(100% - 4px); padding: 18px 50px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                               placeholder="Enter your password" required
                               onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                               onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                        <i class="fas fa-lock" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                        <button type="button" onclick="togglePassword()" style="position: absolute; right: 18px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; font-size: 16px;">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div style="margin-bottom: 30px;">
                    <label style="display: flex; align-items: center; gap: 12px; cursor: pointer; color: white; font-weight: 500;">
                        <input type="checkbox" name="remember_me" style="width: 18px; height: 18px; accent-color: #FF6B6B; transform: scale(1.2);">
                        <span>Remember me for 30 days</span>
                    </label>
                </div>

                <?php if ($error): ?>
                    <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 30px; border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 10px 25px rgba(231,76,60,0.3);">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 20px;"></i>
                            <span style="font-weight: 600;"><?php echo htmlspecialchars($error); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <button type="submit"
                        style="width: 100%; padding: 20px; background: linear-gradient(135deg, #FF6B6B, #FF8E53); color: white; border: none; border-radius: 15px; font-size: 18px; font-weight: 700; cursor: pointer; transition: all 0.4s ease; box-shadow: 0 10px 25px rgba(255,107,107,0.4); margin-bottom: 30px;"
                        onmouseover="this.style.transform='translateY(-3px) scale(1.02)'; this.style.boxShadow='0 15px 35px rgba(255,107,107,0.6)'"
                        onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(255,107,107,0.4)'">
                    <i class="fas fa-sign-in-alt"></i> Sign In to Account
                </button>
            </form>

            <!-- Forgot Password Link -->
            <div style="text-align: center; margin-bottom: 30px;">
                <a href="<?php echo SITE_URL; ?>/auth/forgot-password.php"
                   style="color: rgba(255,255,255,0.9); font-weight: 600; text-decoration: none; padding: 10px 20px; background: rgba(255,255,255,0.1); border-radius: 25px; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);"
                   onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(0)'">
                    <i class="fas fa-key"></i> Forgot your password?
                </a>
            </div>

            <!-- Divider -->
            <div style="text-align: center; margin: 30px 0; position: relative;">
                <div style="height: 1px; background: rgba(255,255,255,0.3); margin: 0 20px;"></div>
                <span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.1); padding: 8px 20px; border-radius: 20px; color: white; font-size: 14px; backdrop-filter: blur(10px);">
                    Don't have an account?
                </span>
            </div>

            <!-- Register Link -->
            <div style="text-align: center;">
                <a href="<?php echo SITE_URL; ?>/auth/register.php"
                   style="display: inline-block; background: rgba(255,255,255,0.9); color: #FF6B6B; padding: 16px 32px; border-radius: 15px; text-decoration: none; font-weight: 700; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 10px 25px rgba(0,0,0,0.1);"
                   onmouseover="this.style.background='white'; this.style.transform='translateY(-3px) scale(1.05)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.2)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'">
                    <i class="fas fa-user-plus"></i> Create New Account
                </a>
            </div>

            <!-- Back to Home -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="<?php echo SITE_URL; ?>/"
                   style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 500; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease;"
                   onmouseover="this.style.color='white'; this.style.background='rgba(255,255,255,0.1)'"
                   onmouseout="this.style.color='rgba(255,255,255,0.8)'; this.style.background='transparent'">
                    <i class="fas fa-arrow-left"></i> Back to Homepage
                </a>
            </div>
        </div>
    </div>
</div>

<style>
/* Login Page Animations */
@keyframes floatSlow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

@keyframes morphing {
    0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; transform: rotate(0deg) scale(1); }
    25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; transform: rotate(90deg) scale(1.1); }
    50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; transform: rotate(180deg) scale(0.9); }
    75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; transform: rotate(270deg) scale(1.05); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px !important;
    }

    .login-container {
        padding: 30px 25px !important;
        margin: 10px !important;
    }

    h1 {
        font-size: 28px !important;
    }

    input {
        padding: 16px 20px 16px 45px !important;
        font-size: 16px !important;
    }

    button {
        padding: 18px !important;
        font-size: 16px !important;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 25px 20px !important;
    }

    h1 {
        font-size: 24px !important;
    }

    input {
        padding: 14px 18px 14px 40px !important;
        font-size: 15px !important;
    }
}
</style>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Add loading state to form
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
    submitBtn.disabled = true;
    submitBtn.style.background = 'linear-gradient(135deg, #95a5a6, #7f8c8d)';
});

// Add entrance animation
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.container > div');
    container.style.animation = 'fadeInUp 0.8s ease-out';

    // Add sparkle effect on mouse move
    document.addEventListener('mousemove', function(e) {
        if (Math.random() > 0.95) {
            const sparkle = document.createElement('div');
            sparkle.style.position = 'fixed';
            sparkle.style.left = e.clientX + 'px';
            sparkle.style.top = e.clientY + 'px';
            sparkle.style.width = '4px';
            sparkle.style.height = '4px';
            sparkle.style.background = 'white';
            sparkle.style.borderRadius = '50%';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkle 1s ease-out forwards';
            document.body.appendChild(sparkle);

            setTimeout(() => sparkle.remove(), 1000);
        }
    });
});

// Add sparkle animation
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkle {
        0% { opacity: 1; transform: scale(0); }
        50% { opacity: 1; transform: scale(1); }
        100% { opacity: 0; transform: scale(0); }
    }
`;
document.head.appendChild(style);

console.log('🎨 Stunning login page loaded!');
console.log('✨ Features: Gradient background, glass morphism, animations');
</script>

<!-- Auth Footer -->
<footer style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 40px 0; margin-top: 60px;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 30px;">
            <!-- Company Info -->
            <div>
                <h3 style="color: #ffffff; font-size: 24px; font-weight: 800; margin-bottom: 20px;">Geu's Galore</h3>
                <p style="color: rgba(255,255,255,0.8); line-height: 1.6; margin-bottom: 20px;">
                    Papua New Guinea's premier online store, offering quality products with excellent service and competitive prices.
                </p>
                <div style="display: flex; gap: 15px;">
                    <a href="#" style="color: rgba(255,255,255,0.8); font-size: 20px; transition: color 0.3s ease;">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" style="color: rgba(255,255,255,0.8); font-size: 20px; transition: color 0.3s ease;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" style="color: rgba(255,255,255,0.8); font-size: 20px; transition: color 0.3s ease;">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h4 style="color: #ffffff; font-size: 18px; font-weight: 700; margin-bottom: 20px;">Quick Links</h4>
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <a href="<?php echo SITE_URL; ?>/" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Home</a>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Shop</a>
                    <a href="<?php echo SITE_URL; ?>/pages/about.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">About Us</a>
                    <a href="<?php echo SITE_URL; ?>/contact.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Contact</a>
                </div>
            </div>

            <!-- Contact Info -->
            <div>
                <h4 style="color: #ffffff; font-size: 18px; font-weight: 700; margin-bottom: 20px;">Contact Info</h4>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: rgba(255,255,255,0.8);">
                        <i class="fas fa-map-marker-alt" style="color: #FF6B6B;"></i>
                        <span>Port Moresby, Papua New Guinea</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px; color: rgba(255,255,255,0.8);">
                        <i class="fas fa-phone" style="color: #FF6B6B;"></i>
                        <span>+************</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px; color: rgba(255,255,255,0.8);">
                        <i class="fas fa-envelope" style="color: #FF6B6B;"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 20px; text-align: center;">
            <p style="color: rgba(255,255,255,0.7); margin: 0; font-size: 14px;">
                © 2025 Geu's Galore. All rights reserved. | Designed for Papua New Guinea
            </p>
        </div>
    </div>
</footer>

</body>
</html>
