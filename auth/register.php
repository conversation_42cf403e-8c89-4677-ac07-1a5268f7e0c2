<?php
require_once '../config/config.php';

$pageTitle = 'Register';
$pageDescription = 'Create your account at Geu\'s Galore and start shopping today.';

// Redirect if already logged in
if (is_logged_in()) {
    redirect(SITE_URL . '/user/dashboard.php');
}

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = new GeusGalore\User();
    $result = $user->register($_POST);
    
    if ($result['success']) {
        $success = true;
        flash_message('success', $result['message']);
    } else {
        $errors = $result['errors'];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">

<!-- Auth Header -->
<header style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 15px 0; position: fixed; top: 0; left: 0; right: 0; z-index: 1000; backdrop-filter: blur(10px);">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center;">
        <a href="<?php echo SITE_URL; ?>/" style="font-size: 28px; font-weight: 900; color: #ffffff; text-decoration: none; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
            Geu's Galore
        </a>
        <div style="display: flex; gap: 20px; align-items: center;">
            <a href="<?php echo SITE_URL; ?>/" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-home"></i> Home
            </a>
            <a href="<?php echo SITE_URL; ?>/shop/products.php" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-shopping-bag"></i> Shop
            </a>
            <a href="<?php echo SITE_URL; ?>/contact.php" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease; font-weight: 500;">
                <i class="fas fa-envelope"></i> Contact
            </a>
        </div>
    </div>
</header>

<!-- Stunning Register Page -->
<div style="min-height: 100vh; background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%); position: relative; display: flex; align-items: center; overflow: hidden; padding: 100px 0 40px 0;">
    <!-- Animated Background -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
        <div style="position: absolute; top: 15%; left: 8%; width: 250px; height: 250px; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); border-radius: 50%; animation: floatSlow 18s ease-in-out infinite;"></div>
        <div style="position: absolute; bottom: 20%; right: 12%; width: 180px; height: 180px; background: radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%); border-radius: 50%; animation: floatSlow 14s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; top: 60%; left: 50%; width: 120px; height: 120px; background: radial-gradient(circle, rgba(255,255,255,0.06) 0%, transparent 70%); border-radius: 50%; animation: floatSlow 20s ease-in-out infinite;"></div>
    </div>

    <!-- Floating Morphing Elements -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; pointer-events: none;">
        <div style="position: absolute; top: 25%; left: 15%; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; animation: morphing 10s ease-in-out infinite;"></div>
        <div style="position: absolute; bottom: 30%; right: 20%; width: 80px; height: 80px; background: rgba(255,255,255,0.08); border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%; animation: morphing 8s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; top: 40%; right: 10%; width: 40px; height: 40px; background: rgba(255,255,255,0.06); border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%; animation: morphing 12s ease-in-out infinite;"></div>
    </div>

    <div class="container" style="max-width: 650px; margin: 0 auto; padding: 20px; position: relative; z-index: 10;">
        <div style="background: rgba(255,255,255,0.15); backdrop-filter: blur(25px); border-radius: 30px; padding: 50px 40px; border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 25px 50px rgba(0,0,0,0.2);">

            <!-- Header Section -->
            <div style="text-align: center; margin-bottom: 40px;">
                <div style="display: inline-block; background: rgba(255,255,255,0.2); backdrop-filter: blur(10px); padding: 12px 24px; border-radius: 50px; margin-bottom: 25px; border: 1px solid rgba(255,255,255,0.3);">
                    <span style="color: white; font-weight: 600; font-size: 12px; text-transform: uppercase; letter-spacing: 1px;">
                        🌟 Join the Family
                    </span>
                </div>

                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1)); border-radius: 30px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; box-shadow: 0 15px 35px rgba(0,0,0,0.2); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-user-plus" style="font-size: 45px; color: white;"></i>
                </div>

                <h1 style="color: white; font-size: 36px; font-weight: 900; margin-bottom: 15px; text-shadow: 2px 2px 20px rgba(0,0,0,0.3);">
                    Create Your Account
                </h1>
                <p style="color: rgba(255,255,255,0.9); font-size: 18px; font-weight: 500;">
                    Join thousands of happy customers in Papua New Guinea
                </p>
            </div>

            <?php if ($success): ?>
                <!-- Success State -->
                <div style="text-align: center; background: linear-gradient(135deg, #6BCF7F, #4ECDC4); color: white; padding: 40px 30px; border-radius: 25px; margin-bottom: 30px; box-shadow: 0 15px 35px rgba(107,207,127,0.4);">
                    <div style="width: 100px; height: 100px; background: rgba(255,255,255,0.2); border-radius: 50%; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px);">
                        <i class="fas fa-check-circle" style="font-size: 50px;"></i>
                    </div>
                    <h2 style="margin-bottom: 20px; font-size: 28px; font-weight: 800;">Registration Successful! 🎉</h2>
                    <p style="margin-bottom: 30px; font-size: 18px; opacity: 0.95;">
                        Welcome to the Geu's Galore family! Please check your email to verify your account and complete the registration process.
                    </p>
                    <a href="<?php echo SITE_URL; ?>/auth/login.php"
                       style="display: inline-block; background: rgba(255,255,255,0.9); color: #6BCF7F; padding: 16px 32px; border-radius: 15px; text-decoration: none; font-weight: 700; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 10px 25px rgba(0,0,0,0.1);"
                       onmouseover="this.style.background='white'; this.style.transform='translateY(-3px) scale(1.05)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0) scale(1)'">
                        <i class="fas fa-sign-in-alt"></i> Go to Login
                    </a>
                </div>
            <?php else: ?>
                <!-- Registration Form -->
                <form method="POST" data-validate>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                        <div>
                            <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                                <i class="fas fa-user"></i> First Name *
                            </label>
                            <div style="position: relative;">
                                <input type="text" id="first_name" name="first_name"
                                       style="width: calc(100% - 4px); padding: 18px 20px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                       placeholder="Enter first name"
                                       value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required
                                       onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-user" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                            </div>
                            <?php if (isset($errors['first_name'])): ?>
                                <div style="color: #ff6b6b; font-size: 14px; margin-top: 8px; display: flex; align-items: center; gap: 5px; background: rgba(255,107,107,0.1); padding: 8px 12px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo $errors['first_name']; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div>
                            <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                                <i class="fas fa-user"></i> Last Name *
                            </label>
                            <div style="position: relative;">
                                <input type="text" id="last_name" name="last_name"
                                       style="width: calc(100% - 4px); padding: 18px 20px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                       placeholder="Enter last name"
                                       value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required
                                       onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-user" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                            </div>
                            <?php if (isset($errors['last_name'])): ?>
                                <div style="color: #ff6b6b; font-size: 14px; margin-top: 8px; display: flex; align-items: center; gap: 5px; background: rgba(255,107,107,0.1); padding: 8px 12px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo $errors['last_name']; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                
                    <!-- Username Field -->
                    <div style="margin-bottom: 25px;">
                        <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                            <i class="fas fa-at"></i> Username *
                        </label>
                        <div style="position: relative;">
                            <input type="text" id="username" name="username"
                                   style="width: calc(100% - 4px); padding: 18px 20px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                   placeholder="Choose a username"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required
                                   onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                   onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                            <i class="fas fa-at" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                        </div>
                        <small style="color: rgba(255,255,255,0.8); font-size: 12px; margin-top: 5px; display: block;">Must be at least 3 characters long</small>
                        <?php if (isset($errors['username'])): ?>
                            <div style="color: #ff6b6b; font-size: 14px; margin-top: 8px; display: flex; align-items: center; gap: 5px; background: rgba(255,107,107,0.1); padding: 8px 12px; border-radius: 8px; backdrop-filter: blur(10px);">
                                <i class="fas fa-exclamation-circle"></i> <?php echo $errors['username']; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Email and Phone Fields -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                        <div>
                            <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                                <i class="fas fa-envelope"></i> Email Address *
                            </label>
                            <div style="position: relative;">
                                <input type="email" id="email" name="email"
                                       style="width: calc(100% - 4px); padding: 18px 20px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                       placeholder="Enter your email"
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required
                                       onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-envelope" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                            </div>
                            <?php if (isset($errors['email'])): ?>
                                <div style="color: #ff6b6b; font-size: 14px; margin-top: 8px; display: flex; align-items: center; gap: 5px; background: rgba(255,107,107,0.1); padding: 8px 12px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo $errors['email']; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div>
                            <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                                <i class="fas fa-phone"></i> Phone Number
                            </label>
                            <div style="position: relative;">
                                <input type="tel" id="phone" name="phone"
                                       style="width: calc(100% - 4px); padding: 18px 20px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                       placeholder="+************"
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                       onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-phone" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                            </div>
                        </div>
                    </div>
                
                    <!-- Password Fields -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                        <div>
                            <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                                <i class="fas fa-lock"></i> Password *
                            </label>
                            <div style="position: relative;">
                                <input type="password" id="password" name="password"
                                       style="width: calc(100% - 4px); padding: 18px 50px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                       placeholder="Create password" required
                                       onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-lock" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                                <button type="button" onclick="togglePassword('password')" style="position: absolute; right: 18px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; font-size: 16px;">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            <small style="color: rgba(255,255,255,0.8); font-size: 12px; margin-top: 5px; display: block;">Must be at least 6 characters long</small>
                            <?php if (isset($errors['password'])): ?>
                                <div style="color: #ff6b6b; font-size: 14px; margin-top: 8px; display: flex; align-items: center; gap: 5px; background: rgba(255,107,107,0.1); padding: 8px 12px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo $errors['password']; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div>
                            <label style="display: block; color: white; font-weight: 600; margin-bottom: 8px; font-size: 14px;">
                                <i class="fas fa-lock"></i> Confirm Password *
                            </label>
                            <div style="position: relative;">
                                <input type="password" id="confirm_password" name="confirm_password"
                                       style="width: calc(100% - 4px); padding: 18px 50px 18px 50px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; font-size: 16px; background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); transition: all 0.3s ease; outline: none; box-sizing: border-box;"
                                       placeholder="Confirm password" required
                                       onfocus="this.style.borderColor='rgba(255,255,255,0.8)'; this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'"
                                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-lock" style="position: absolute; left: 18px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
                                <button type="button" onclick="togglePassword('confirm_password')" style="position: absolute; right: 18px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; font-size: 16px;">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Terms Agreement -->
                    <div style="margin-bottom: 30px;">
                        <label style="display: flex; align-items: flex-start; gap: 12px; cursor: pointer; color: white; font-weight: 500; line-height: 1.5;">
                            <input type="checkbox" name="agree_terms" required style="width: 18px; height: 18px; accent-color: #f093fb; transform: scale(1.2); margin-top: 2px;">
                            <span>I agree to the <a href="<?php echo SITE_URL; ?>/help/terms.php" target="_blank" style="color: #FFD93D; text-decoration: none; font-weight: 600;">Terms of Service</a> and <a href="<?php echo SITE_URL; ?>/help/privacy.php" target="_blank" style="color: #FFD93D; text-decoration: none; font-weight: 600;">Privacy Policy</a></span>
                        </label>
                    </div>

                    <!-- Error Message -->
                    <?php if (isset($errors['general'])): ?>
                        <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 30px; border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 10px 25px rgba(231,76,60,0.3);">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 20px;"></i>
                                <span style="font-weight: 600;"><?php echo $errors['general']; ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Submit Button -->
                    <button type="submit"
                            style="width: 100%; padding: 20px; background: linear-gradient(135deg, #f093fb, #f5576c); color: white; border: none; border-radius: 15px; font-size: 18px; font-weight: 700; cursor: pointer; transition: all 0.4s ease; box-shadow: 0 10px 25px rgba(240,147,251,0.4); margin-bottom: 30px;"
                            onmouseover="this.style.transform='translateY(-3px) scale(1.02)'; this.style.boxShadow='0 15px 35px rgba(240,147,251,0.6)'"
                            onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(240,147,251,0.4)'">
                        <i class="fas fa-user-plus"></i> Create My Account
                    </button>
                </form>

                <!-- Divider -->
                <div style="text-align: center; margin: 30px 0; position: relative;">
                    <div style="height: 1px; background: rgba(255,255,255,0.3); margin: 0 20px;"></div>
                    <span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.1); padding: 8px 20px; border-radius: 20px; color: white; font-size: 14px; backdrop-filter: blur(10px);">
                        Already have an account?
                    </span>
                </div>

                <!-- Login Link -->
                <div style="text-align: center; margin-bottom: 30px;">
                    <a href="<?php echo SITE_URL; ?>/auth/login.php"
                       style="display: inline-block; background: rgba(255,255,255,0.9); color: #f093fb; padding: 16px 32px; border-radius: 15px; text-decoration: none; font-weight: 700; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 10px 25px rgba(0,0,0,0.1);"
                       onmouseover="this.style.background='white'; this.style.transform='translateY(-3px) scale(1.05)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.2)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.1)'">
                        <i class="fas fa-sign-in-alt"></i> Sign In Instead
                    </a>
                </div>

                <!-- Back to Home -->
                <div style="text-align: center;">
                    <a href="<?php echo SITE_URL; ?>/"
                       style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 500; padding: 8px 16px; border-radius: 20px; transition: all 0.3s ease;"
                       onmouseover="this.style.color='white'; this.style.background='rgba(255,255,255,0.1)'"
                       onmouseout="this.style.color='rgba(255,255,255,0.8)'; this.style.background='transparent'">
                        <i class="fas fa-arrow-left"></i> Back to Homepage
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Register Page Animations */
@keyframes floatSlow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

@keyframes morphing {
    0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; transform: rotate(0deg) scale(1); }
    25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; transform: rotate(90deg) scale(1.1); }
    50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; transform: rotate(180deg) scale(0.9); }
    75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; transform: rotate(270deg) scale(1.05); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px !important;
    }

    .register-container {
        padding: 30px 25px !important;
        margin: 10px !important;
    }

    h1 {
        font-size: 28px !important;
    }

    input {
        padding: 16px 20px 16px 45px !important;
        font-size: 16px !important;
    }

    button {
        padding: 18px !important;
        font-size: 16px !important;
    }

    /* Stack form fields on mobile */
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
}

@media (max-width: 480px) {
    .register-container {
        padding: 25px 20px !important;
    }

    h1 {
        font-size: 24px !important;
    }

    input {
        padding: 14px 18px 14px 40px !important;
        font-size: 15px !important;
    }
}
</style>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = fieldId === 'password' ? document.getElementById('toggleIcon1') : document.getElementById('toggleIcon2');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.style.borderColor = '#ff6b6b';
    } else {
        this.setCustomValidity('');
        this.style.borderColor = 'rgba(255,255,255,0.3)';
    }
});

// Username validation
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    if (username.length > 0 && username.length < 3) {
        this.setCustomValidity('Username must be at least 3 characters long');
        this.style.borderColor = '#ff6b6b';
    } else {
        this.setCustomValidity('');
        this.style.borderColor = 'rgba(255,255,255,0.3)';
    }
});

// Password strength validation
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    if (password.length > 0 && password.length < 6) {
        this.setCustomValidity('Password must be at least 6 characters long');
        this.style.borderColor = '#ff6b6b';
    } else {
        this.setCustomValidity('');
        this.style.borderColor = 'rgba(255,255,255,0.3)';
    }
});

// Add loading state to form
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
    submitBtn.disabled = true;
    submitBtn.style.background = 'linear-gradient(135deg, #95a5a6, #7f8c8d)';
});

// Add entrance animation
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.container > div');
    container.style.animation = 'fadeInUp 0.8s ease-out';

    // Add sparkle effect on mouse move
    document.addEventListener('mousemove', function(e) {
        if (Math.random() > 0.95) {
            const sparkle = document.createElement('div');
            sparkle.style.position = 'fixed';
            sparkle.style.left = e.clientX + 'px';
            sparkle.style.top = e.clientY + 'px';
            sparkle.style.width = '4px';
            sparkle.style.height = '4px';
            sparkle.style.background = 'white';
            sparkle.style.borderRadius = '50%';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkle 1s ease-out forwards';
            document.body.appendChild(sparkle);

            setTimeout(() => sparkle.remove(), 1000);
        }
    });
});

// Add sparkle animation
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkle {
        0% { opacity: 1; transform: scale(0); }
        50% { opacity: 1; transform: scale(1); }
        100% { opacity: 0; transform: scale(0); }
    }
`;
document.head.appendChild(style);

console.log('🎨 Stunning register page loaded!');
console.log('✨ Features: Gradient background, glass morphism, animations, validation');
</script>

<!-- Auth Footer -->
<footer style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 40px 0; margin-top: 60px;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 30px;">
            <!-- Company Info -->
            <div>
                <h3 style="color: #ffffff; font-size: 24px; font-weight: 800; margin-bottom: 20px;">Geu's Galore</h3>
                <p style="color: rgba(255,255,255,0.8); line-height: 1.6; margin-bottom: 20px;">
                    Papua New Guinea's premier online store, offering quality products with excellent service and competitive prices.
                </p>
                <div style="display: flex; gap: 15px;">
                    <a href="#" style="color: rgba(255,255,255,0.8); font-size: 20px; transition: color 0.3s ease;">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" style="color: rgba(255,255,255,0.8); font-size: 20px; transition: color 0.3s ease;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" style="color: rgba(255,255,255,0.8); font-size: 20px; transition: color 0.3s ease;">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h4 style="color: #ffffff; font-size: 18px; font-weight: 700; margin-bottom: 20px;">Quick Links</h4>
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <a href="<?php echo SITE_URL; ?>/" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Home</a>
                    <a href="<?php echo SITE_URL; ?>/shop/products.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Shop</a>
                    <a href="<?php echo SITE_URL; ?>/pages/about.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">About Us</a>
                    <a href="<?php echo SITE_URL; ?>/contact.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">Contact</a>
                </div>
            </div>

            <!-- Contact Info -->
            <div>
                <h4 style="color: #ffffff; font-size: 18px; font-weight: 700; margin-bottom: 20px;">Contact Info</h4>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: rgba(255,255,255,0.8);">
                        <i class="fas fa-map-marker-alt" style="color: #FF6B6B;"></i>
                        <span>Port Moresby, Papua New Guinea</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px; color: rgba(255,255,255,0.8);">
                        <i class="fas fa-phone" style="color: #FF6B6B;"></i>
                        <span>+************</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px; color: rgba(255,255,255,0.8);">
                        <i class="fas fa-envelope" style="color: #FF6B6B;"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 20px; text-align: center;">
            <p style="color: rgba(255,255,255,0.7); margin: 0; font-size: 14px;">
                © 2025 Geu's Galore. All rights reserved. | Designed for Papua New Guinea
            </p>
        </div>
    </div>
</footer>

</body>
</html>
