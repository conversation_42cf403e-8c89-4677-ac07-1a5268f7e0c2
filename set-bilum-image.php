<?php
require_once 'config/config.php';

echo "<h1>🖼️ Set Bilum Image for All Products</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; border: none; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
    .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f8f9fa; text-align: center; }
    .product-card img { width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 10px; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    
    // Check if we should apply the changes
    $applyChanges = isset($_GET['apply']) && $_GET['apply'] === 'yes';
    
    echo "<h2>🔍 Checking for bilum.webp Image</h2>";
    
    // Check if bilum.webp exists
    $uploadsDir = ROOT_PATH . '/uploads/products/';
    $bilumPath = $uploadsDir . 'bilum.webp';
    $bilumUrl = UPLOAD_URL . 'products/bilum.webp';
    
    if (!file_exists($bilumPath)) {
        echo "<div class='error'>❌ bilum.webp not found in uploads/products/</div>";
        
        // Try to find any bilum-related files
        $bilumFiles = glob($uploadsDir . '*bilum*');
        if (!empty($bilumFiles)) {
            echo "<div class='info'>📁 Found bilum-related files:</div>";
            echo "<ul>";
            foreach ($bilumFiles as $file) {
                $filename = basename($file);
                echo "<li>$filename</li>";
            }
            echo "</ul>";
            
            // Use the first bilum file found
            $sourceBilum = $bilumFiles[0];
            $sourceFilename = basename($sourceBilum);
            
            if ($applyChanges) {
                // Copy/rename to bilum.webp
                if (copy($sourceBilum, $bilumPath)) {
                    echo "<div class='success'>✅ Copied $sourceFilename to bilum.webp</div>";
                } else {
                    echo "<div class='error'>❌ Failed to copy $sourceFilename to bilum.webp</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ Will use $sourceFilename as bilum.webp when you apply changes</div>";
            }
        } else {
            echo "<div class='error'>❌ No bilum-related files found. Please upload bilum.webp to uploads/products/</div>";
            echo "<div class='info'>💡 You can upload the file manually or place it in the uploads/products/ directory</div>";
        }
    } else {
        echo "<div class='success'>✅ bilum.webp found: " . number_format(filesize($bilumPath)) . " bytes</div>";
        echo "<div class='info'>📍 File location: $bilumPath</div>";
        echo "<div class='info'>🌐 URL: $bilumUrl</div>";
        
        // Show preview
        echo "<h3>🖼️ Image Preview</h3>";
        echo "<img src='$bilumUrl' style='max-width: 300px; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);' alt='Bilum Image'>";
    }
    
    // Get all products
    echo "<h2>🛍️ Current Products</h2>";
    $products = $db->fetchAll("SELECT * FROM products ORDER BY id");
    echo "<div class='info'>📊 Total products: " . count($products) . "</div>";
    
    if ($applyChanges && file_exists($bilumPath)) {
        echo "<h3>🔄 Updating All Products to Use bilum.webp</h3>";
        
        // Clear existing product images
        $db->query("DELETE FROM product_images");
        echo "<div class='success'>✅ Cleared existing product images</div>";
        
        // Add bilum.webp to all products
        $successCount = 0;
        foreach ($products as $product) {
            try {
                $db->insert('product_images', [
                    'product_id' => $product['id'],
                    'image_path' => 'bilum.webp',
                    'alt_text' => $product['name'],
                    'is_primary' => 1,
                    'sort_order' => 1
                ]);
                echo "<div class='success'>✅ Updated: {$product['name']}</div>";
                $successCount++;
            } catch (Exception $e) {
                echo "<div class='error'>❌ Failed to update {$product['name']}: " . $e->getMessage() . "</div>";
            }
        }
        
        echo "<h3>📊 Update Summary</h3>";
        echo "<div class='success'>✅ Successfully updated $successCount out of " . count($products) . " products</div>";
        
        // Show updated products preview
        echo "<h3>🎉 Updated Products Preview</h3>";
        $updatedProducts = $db->fetchAll("
            SELECT p.*, pi.image_path 
            FROM products p 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
            ORDER BY p.id 
            LIMIT 6
        ");
        
        echo "<div class='product-grid'>";
        foreach ($updatedProducts as $product) {
            echo "<div class='product-card'>";
            echo "<img src='$bilumUrl' alt='{$product['name']}'>";
            echo "<h4>{$product['name']}</h4>";
            echo "<p><strong>PGK " . number_format($product['price'], 2) . "</strong></p>";
            if ($product['sale_price']) {
                echo "<p style='color: #dc3545;'>Sale: PGK " . number_format($product['sale_price'], 2) . "</p>";
            }
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        // Show current products preview
        echo "<h3>🔍 Current Products Preview</h3>";
        $currentProducts = $db->fetchAll("
            SELECT p.*, pi.image_path 
            FROM products p 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
            ORDER BY p.id 
            LIMIT 6
        ");
        
        echo "<div class='product-grid'>";
        foreach ($currentProducts as $product) {
            echo "<div class='product-card'>";
            if ($product['image_path']) {
                $currentImageUrl = UPLOAD_URL . 'products/' . $product['image_path'];
                echo "<img src='$currentImageUrl' alt='{$product['name']}'>";
                echo "<div class='info'>Current: {$product['image_path']}</div>";
            } else {
                echo "<div style='height: 200px; background: #ddd; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-bottom: 10px;'>";
                echo "<span style='color: #666;'>No image</span>";
                echo "</div>";
            }
            echo "<h4>{$product['name']}</h4>";
            echo "<p><strong>PGK " . number_format($product['price'], 2) . "</strong></p>";
            if ($product['sale_price']) {
                echo "<p style='color: #dc3545;'>Sale: PGK " . number_format($product['sale_price'], 2) . "</p>";
            }
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Action buttons
    echo "<h3>🚀 Actions</h3>";
    
    if (!$applyChanges) {
        if (file_exists($bilumPath)) {
            echo "<div class='warning'>";
            echo "<strong>⚠️ Ready to Update All Products</strong><br>";
            echo "This will replace ALL product images with bilum.webp<br>";
            echo "All products will use the same image across all views (admin, product detail, etc.)";
            echo "</div>";
            echo "<p><a href='?apply=yes' class='btn btn-success'>🔄 Apply bilum.webp to All Products</a></p>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ Cannot Proceed</strong><br>";
            echo "bilum.webp file not found. Please upload it to uploads/products/ directory first.";
            echo "</div>";
        }
    }
    
    echo "<p><a href='shop/products.php' class='btn'>🛍️ View Products Page</a></p>";
    echo "<p><a href='admin/products.php' class='btn'>⚙️ Admin Products</a></p>";
    
    // Image sizing information
    echo "<h3>📐 Image Sizing Information</h3>";
    echo "<div class='info'>";
    echo "<strong>The bilum.webp image will be automatically sized for different views:</strong><br><br>";
    echo "🖥️ <strong>Desktop Product Grid:</strong> 380px width, 280px height<br>";
    echo "📱 <strong>Mobile Product Grid:</strong> Full width, responsive height<br>";
    echo "👁️ <strong>Product Detail View:</strong> Large display with zoom capability<br>";
    echo "⚙️ <strong>Admin View:</strong> Thumbnail and full-size preview<br>";
    echo "🔍 <strong>Admin Product List:</strong> Small thumbnails for quick identification<br><br>";
    echo "📝 <strong>Note:</strong> The same image file will be used everywhere, but CSS will handle the sizing appropriately for each view.";
    echo "</div>";
    
    if ($applyChanges && file_exists($bilumPath)) {
        echo "<h3>✅ All Done!</h3>";
        echo "<div class='success'>";
        echo "✅ All products now use bilum.webp<br>";
        echo "✅ Image will display properly in all views<br>";
        echo "✅ Consistent product imagery across the site<br>";
        echo "✅ Admin and customer views updated<br>";
        echo "</div>";
        
        echo "<h4>🧪 Test These Views:</h4>";
        echo "<ul>";
        echo "<li><a href='shop/products.php' target='_blank'>🛍️ Customer Product Grid</a></li>";
        echo "<li><a href='admin/products.php' target='_blank'>⚙️ Admin Product Management</a></li>";
        echo "<li>🔍 Individual product detail pages</li>";
        echo "<li>📱 Mobile responsive views</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    
    echo "<h3>🔧 Manual Steps:</h3>";
    echo "<ol>";
    echo "<li>Upload bilum.webp to uploads/products/ directory</li>";
    echo "<li>Run this script with ?apply=yes</li>";
    echo "<li>Check products page to verify images display</li>";
    echo "<li>Test admin product management</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file after use!";
echo "</p>";

echo "</div>";
?>
