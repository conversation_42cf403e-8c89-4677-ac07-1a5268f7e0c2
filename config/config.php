<?php
// <PERSON><PERSON>'s Galore Configuration File

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Site configuration
define('SITE_NAME', "Geu's Galore");
// Auto-detect site URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$path = rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
define('SITE_URL', $protocol . '://' . $host . $path);
define('SITE_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'u787474055_geugalore');
define('DB_USER', 'u787474055_geugalore');
define('DB_PASS', 'Blackpanther707@707');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/assets/uploads/');
define('UPLOAD_URL', SITE_URL . '/assets/uploads/');

// Security
define('ENCRYPTION_KEY', 'geus_galore_2024_secure_key_png_store_v1');
define('JWT_SECRET', 'jwt_secret_geus_galore_blackpanther_2024');

// Email configuration (SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');

// Stripe configuration
define('STRIPE_PUBLISHABLE_KEY', 'pk_test_your_stripe_publishable_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');

// Pagination
define('PRODUCTS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Color palette
define('COLORS', [
    'white' => '#FFFFFF',
    'cream' => '#E6DCD3',
    'light_brown' => '#B4A79E',
    'medium_brown' => '#BDA18C',
    'dark_brown' => '#3F352C',
    'black' => '#000000'
]);

// Timezone
date_default_timezone_set('Pacific/Port_Moresby');

// Auto-load classes
spl_autoload_register(function ($class) {
    $class = str_replace('GeusGalore\\', '', $class);
    $file = ROOT_PATH . '/classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Include Composer autoloader if available
if (file_exists(ROOT_PATH . '/vendor/autoload.php')) {
    require_once ROOT_PATH . '/vendor/autoload.php';
}

// Helper functions
function redirect($url) {
    header("Location: $url");
    exit();
}

function flash_message($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

function get_flash_message($type) {
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}

function is_logged_in() {
    return isset($_SESSION['user_id']);
}

function is_admin() {
    return isset($_SESSION['admin_id']);
}

function current_user_id() {
    return $_SESSION['user_id'] ?? null;
}

function current_admin_id() {
    return $_SESSION['admin_id'] ?? null;
}

function sanitize_input($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function format_currency($amount) {
    return 'PGK ' . number_format($amount, 2);
}

function generate_slug($string) {
    return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string), '-'));
}

function generate_random_string($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

function resize_image($source, $destination, $max_width = 800, $max_height = 600) {
    $info = getimagesize($source);
    $width = $info[0];
    $height = $info[1];
    $type = $info[2];
    
    // Calculate new dimensions
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = $width * $ratio;
    $new_height = $height * $ratio;
    
    // Create new image
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($new_image, $destination, 90);
            break;
        case IMAGETYPE_PNG:
            imagepng($new_image, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($new_image, $destination);
            break;
    }
    
    imagedestroy($source_image);
    imagedestroy($new_image);
    
    return true;
}
?>
