<?php
require_once 'config/config.php';

echo "<h1>🔧 Admin Login Fix Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .btn { display: inline-block; padding: 12px 24px; margin: 10px 5px; text-decoration: none; border-radius: 5px; font-weight: bold; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .credentials { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff; }
</style>";

echo "<div class='container'>";

try {
    echo "<h2>Step 1: Database Connection</h2>";
    $db = GeusGalore\Database::getInstance();
    echo "<p class='success'>✅ Database connected successfully!</p>";
    
    echo "<h2>Step 2: Reset Admin Password</h2>";
    
    // Generate new password hash
    $newPassword = 'admin123';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    echo "<p class='info'>🔄 Generating new password hash...</p>";
    echo "<p class='info'>🔑 New password will be: <strong>admin123</strong></p>";
    
    // Use direct PDO query to avoid any issues
    $pdo = $db->getConnection();
    $stmt = $pdo->prepare("UPDATE admin_users SET password = ? WHERE username = 'admin'");
    $result = $stmt->execute([$hashedPassword]);
    
    if ($result) {
        echo "<p class='success'>✅ Password updated successfully!</p>";
        
        // Verify the update worked
        echo "<h2>Step 3: Verification Test</h2>";
        $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($newPassword, $admin['password'])) {
            echo "<p class='success'>✅ Password verification successful!</p>";
            echo "<p class='success'>🎉 Admin login is now fixed!</p>";
            
            echo "<div class='credentials'>";
            echo "<h3>🔐 Your Admin Login Credentials:</h3>";
            echo "<p><strong>URL:</strong> <a href='admin/login.php' target='_blank'>admin/login.php</a></p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Alternative:</strong> <EMAIL></p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            echo "</div>";
            
            echo "<h2>✅ SUCCESS - Ready to Login!</h2>";
            echo "<p>Your admin login is now working. You can use either 'admin' or '<EMAIL>' as the username.</p>";
            
        } else {
            echo "<p class='error'>❌ Verification failed - something went wrong</p>";
        }
        
    } else {
        echo "<p class='error'>❌ Failed to update password</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h3>🔧 Manual Fix Instructions:</h3>";
    echo "<ol>";
    echo "<li>Access your database (phpMyAdmin)</li>";
    echo "<li>Go to the 'admin_users' table</li>";
    echo "<li>Find the user with username 'admin'</li>";
    echo "<li>Update the password field with this hash:</li>";
    echo "<code style='background:#f8f9fa;padding:10px;display:block;margin:10px 0;word-break:break-all;'>";
    echo htmlspecialchars(password_hash('admin123', PASSWORD_DEFAULT));
    echo "</code>";
    echo "<li>Save the changes</li>";
    echo "<li>Try logging in with username: admin, password: admin123</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<h2>🚀 Next Steps:</h2>";
echo "<a href='admin/login.php' class='btn btn-primary'>🔐 Try Admin Login</a>";
echo "<a href='index.php' class='btn btn-success'>🏠 Back to Homepage</a>";

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file (fix-admin-login.php) after successfully logging in!";
echo "</p>";

echo "</div>";
?>
