<?php
require_once 'config/config.php';

echo "<h1>📁 Move Bilum Image Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; border: none; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .file-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #007bff; }
</style>";

echo "<div class='container'>";

try {
    // Check if we should move the file
    $moveFile = isset($_GET['move']) && $_GET['move'] === 'yes';
    
    echo "<h2>🔍 Searching for bilum.webp</h2>";
    
    // Define search locations
    $searchPaths = [
        ROOT_PATH . '/bilum.webp',                    // Root directory
        ROOT_PATH . '/assets/bilum.webp',             // Assets directory
        ROOT_PATH . '/assets/images/bilum.webp',      // Assets/images directory
        ROOT_PATH . '/images/bilum.webp',             // Images directory
        ROOT_PATH . '/uploads/bilum.webp',            // Uploads directory
        ROOT_PATH . '/public/bilum.webp',             // Public directory
    ];
    
    // Also search for any bilum files (jpg, png, etc.)
    $bilumPatterns = [
        ROOT_PATH . '/bilum.*',
        ROOT_PATH . '/assets/bilum.*',
        ROOT_PATH . '/assets/images/bilum.*',
        ROOT_PATH . '/images/bilum.*',
        ROOT_PATH . '/uploads/bilum.*',
        ROOT_PATH . '/public/bilum.*',
    ];
    
    $foundFiles = [];
    
    // Search for exact bilum.webp files
    echo "<h3>📍 Searching in common locations...</h3>";
    foreach ($searchPaths as $path) {
        if (file_exists($path)) {
            $foundFiles[] = $path;
            echo "<div class='success'>✅ Found: $path</div>";
        } else {
            echo "<div class='info'>❌ Not found: $path</div>";
        }
    }
    
    // Search for any bilum files with glob patterns
    echo "<h3>🔍 Searching for any bilum files...</h3>";
    foreach ($bilumPatterns as $pattern) {
        $matches = glob($pattern);
        foreach ($matches as $match) {
            if (!in_array($match, $foundFiles)) {
                $foundFiles[] = $match;
                echo "<div class='success'>✅ Found bilum file: $match</div>";
            }
        }
    }
    
    // Check destination
    $destinationDir = ROOT_PATH . '/uploads/products/';
    $destinationFile = $destinationDir . 'bilum.webp';
    
    echo "<h3>📂 Destination Check</h3>";
    if (!file_exists($destinationDir)) {
        mkdir($destinationDir, 0755, true);
        echo "<div class='success'>✅ Created destination directory: $destinationDir</div>";
    } else {
        echo "<div class='success'>✅ Destination directory exists: $destinationDir</div>";
    }
    
    if (file_exists($destinationFile)) {
        echo "<div class='warning'>⚠️ bilum.webp already exists in destination</div>";
        echo "<div class='file-info'>";
        echo "<strong>Existing file:</strong> $destinationFile<br>";
        echo "<strong>Size:</strong> " . number_format(filesize($destinationFile)) . " bytes<br>";
        echo "<strong>Modified:</strong> " . date('Y-m-d H:i:s', filemtime($destinationFile));
        echo "</div>";
    }
    
    if (empty($foundFiles)) {
        echo "<div class='error'>❌ No bilum files found in common locations</div>";
        
        echo "<h3>🔍 Manual Search</h3>";
        echo "<div class='info'>Let's search the entire project directory for any bilum files...</div>";
        
        // Recursive search for bilum files
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(ROOT_PATH, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        $bilumFiles = [];
        foreach ($iterator as $file) {
            if ($file->isFile() && stripos($file->getFilename(), 'bilum') !== false) {
                $bilumFiles[] = $file->getPathname();
            }
        }
        
        if (!empty($bilumFiles)) {
            echo "<div class='success'>✅ Found " . count($bilumFiles) . " bilum files in project:</div>";
            foreach ($bilumFiles as $file) {
                echo "<div class='file-info'>";
                echo "<strong>File:</strong> $file<br>";
                echo "<strong>Size:</strong> " . number_format(filesize($file)) . " bytes<br>";
                echo "<strong>Type:</strong> " . pathinfo($file, PATHINFO_EXTENSION);
                echo "</div>";
                $foundFiles[] = $file;
            }
        } else {
            echo "<div class='error'>❌ No bilum files found anywhere in the project</div>";
            echo "<div class='warning'>";
            echo "<strong>📝 Manual Steps Required:</strong><br>";
            echo "1. Upload bilum.webp to the root directory of your project<br>";
            echo "2. Or place it in uploads/products/ directly<br>";
            echo "3. Then run this script again";
            echo "</div>";
        }
    } else {
        echo "<h3>📋 Found Files Summary</h3>";
        echo "<div class='info'>Found " . count($foundFiles) . " bilum files:</div>";
        
        foreach ($foundFiles as $index => $file) {
            $filename = basename($file);
            $size = filesize($file);
            $extension = pathinfo($file, PATHINFO_EXTENSION);
            
            echo "<div class='file-info'>";
            echo "<strong>Option " . ($index + 1) . ":</strong> $file<br>";
            echo "<strong>Filename:</strong> $filename<br>";
            echo "<strong>Size:</strong> " . number_format($size) . " bytes<br>";
            echo "<strong>Type:</strong> $extension<br>";
            echo "<strong>Modified:</strong> " . date('Y-m-d H:i:s', filemtime($file));
            echo "</div>";
        }
        
        if ($moveFile) {
            echo "<h3>🚚 Moving File</h3>";
            
            // Use the first found file (or prioritize .webp files)
            $sourceFile = $foundFiles[0];
            
            // Prioritize .webp files if available
            foreach ($foundFiles as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'webp') {
                    $sourceFile = $file;
                    break;
                }
            }
            
            echo "<div class='info'>📁 Source: $sourceFile</div>";
            echo "<div class='info'>📁 Destination: $destinationFile</div>";
            
            if (copy($sourceFile, $destinationFile)) {
                echo "<div class='success'>✅ Successfully copied bilum file to uploads/products/bilum.webp</div>";
                
                // Show file info
                echo "<div class='file-info'>";
                echo "<strong>New file:</strong> $destinationFile<br>";
                echo "<strong>Size:</strong> " . number_format(filesize($destinationFile)) . " bytes<br>";
                echo "<strong>URL:</strong> " . UPLOAD_URL . "products/bilum.webp";
                echo "</div>";
                
                // Show preview
                echo "<h3>🖼️ Image Preview</h3>";
                $imageUrl = UPLOAD_URL . 'products/bilum.webp';
                echo "<img src='$imageUrl' style='max-width: 300px; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);' alt='Bilum Image'>";
                
                echo "<h3>🎉 Success!</h3>";
                echo "<div class='success'>";
                echo "✅ bilum.webp is now ready in uploads/products/<br>";
                echo "✅ You can now run the bilum setup tool<br>";
                echo "✅ All products can use this image";
                echo "</div>";
                
                echo "<h3>🚀 Next Steps</h3>";
                echo "<p><a href='set-bilum-image.php' class='btn btn-success'>🖼️ Set Up Bilum for All Products</a></p>";
                echo "<p><a href='shop/products.php' class='btn'>🛍️ View Products Page</a></p>";
                
            } else {
                echo "<div class='error'>❌ Failed to copy file. Check permissions.</div>";
            }
        } else {
            echo "<h3>🚀 Ready to Move</h3>";
            echo "<div class='warning'>";
            echo "<strong>⚠️ Ready to move bilum file</strong><br>";
            echo "This will copy the bilum file to uploads/products/bilum.webp<br>";
            echo "Source: " . $foundFiles[0];
            echo "</div>";
            echo "<p><a href='?move=yes' class='btn btn-success'>🚚 Move bilum.webp to uploads/products/</a></p>";
        }
    }
    
    // Show current status
    echo "<h3>📊 Current Status</h3>";
    echo "<div class='info'>";
    echo "<strong>Project Root:</strong> " . ROOT_PATH . "<br>";
    echo "<strong>Uploads Directory:</strong> " . $destinationDir . "<br>";
    echo "<strong>Target File:</strong> " . $destinationFile . "<br>";
    echo "<strong>File Exists:</strong> " . (file_exists($destinationFile) ? 'Yes' : 'No');
    echo "</div>";
    
    if (file_exists($destinationFile)) {
        echo "<div class='success'>";
        echo "✅ bilum.webp is ready!<br>";
        echo "✅ You can now set it up for all products";
        echo "</div>";
        echo "<p><a href='set-bilum-image.php' class='btn btn-success'>🖼️ Set Up Bilum for All Products</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    
    echo "<h3>🔧 Manual Steps:</h3>";
    echo "<ol>";
    echo "<li>Find bilum.webp in your project files</li>";
    echo "<li>Copy it to uploads/products/ directory</li>";
    echo "<li>Make sure the file is named exactly 'bilum.webp'</li>";
    echo "<li>Run the bilum setup tool</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file after use!";
echo "</p>";

echo "</div>";
?>
