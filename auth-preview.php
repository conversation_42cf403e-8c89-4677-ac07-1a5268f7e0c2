<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Auth Pages Redesigned - <PERSON><PERSON>'s Galore</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-container {
            max-width: 900px;
            padding: 60px 40px;
            text-align: center;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }
        
        .preview-title {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #FFD93D, #FF8E53);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .preview-subtitle {
            font-size: 24px;
            margin-bottom: 40px;
            opacity: 0.9;
            font-weight: 600;
        }
        
        .preview-description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 50px;
            opacity: 0.85;
        }
        
        .preview-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .preview-feature {
            background: rgba(255,255,255,0.1);
            padding: 30px 20px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .preview-feature:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.15);
        }
        
        .preview-feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }
        
        .preview-feature-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .preview-feature-desc {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .preview-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .preview-btn {
            padding: 18px 36px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .preview-btn-primary {
            background: rgba(255,255,255,0.9);
            color: #667eea;
        }
        
        .preview-btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .preview-btn-secondary {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 2px solid rgba(255,255,255,0.5);
        }
        
        .preview-btn-secondary:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-3px) scale(1.05);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }
        
        .comparison-item {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .comparison-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .comparison-before {
            border-left: 4px solid #e74c3c;
        }
        
        .comparison-after {
            border-left: 4px solid #27ae60;
        }
        
        @media (max-width: 768px) {
            .preview-container {
                margin: 20px;
                padding: 40px 30px;
            }
            
            .preview-title {
                font-size: 36px;
            }
            
            .preview-subtitle {
                font-size: 20px;
            }
            
            .preview-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .preview-btn {
                width: 100%;
                max-width: 300px;
            }
            
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 class="preview-title">🔐 Auth Pages Redesigned!</h1>
        
        <p class="preview-subtitle">
            Login & Register pages now match your stunning homepage!
        </p>
        
        <p class="preview-description">
            I've completely transformed your authentication pages with the same beautiful design language 
            as your homepage. They now feature stunning gradients, glass morphism effects, smooth animations, 
            and perfect mobile responsiveness.
        </p>
        
        <div class="preview-features">
            <div class="preview-feature">
                <div class="preview-feature-icon">🌈</div>
                <div class="preview-feature-title">Matching Design</div>
                <div class="preview-feature-desc">Same beautiful gradients and styling as homepage</div>
            </div>
            
            <div class="preview-feature">
                <div class="preview-feature-icon">🔮</div>
                <div class="preview-feature-title">Glass Morphism</div>
                <div class="preview-feature-desc">Modern backdrop blur and transparency effects</div>
            </div>
            
            <div class="preview-feature">
                <div class="preview-feature-icon">✨</div>
                <div class="preview-feature-title">Smooth Animations</div>
                <div class="preview-feature-desc">Floating elements and interactive hover effects</div>
            </div>
            
            <div class="preview-feature">
                <div class="preview-feature-icon">📱</div>
                <div class="preview-feature-title">Mobile Perfect</div>
                <div class="preview-feature-desc">Responsive design optimized for all devices</div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="comparison-item comparison-before">
                <div class="comparison-title">❌ Before</div>
                <ul style="text-align: left; opacity: 0.8;">
                    <li>Plain, boring forms</li>
                    <li>Basic styling</li>
                    <li>No animations</li>
                    <li>Didn't match homepage</li>
                    <li>Poor mobile experience</li>
                </ul>
            </div>
            
            <div class="comparison-item comparison-after">
                <div class="comparison-title">✅ After</div>
                <ul style="text-align: left; opacity: 0.8;">
                    <li>Stunning gradient backgrounds</li>
                    <li>Glass morphism effects</li>
                    <li>Smooth animations</li>
                    <li>Matches homepage perfectly</li>
                    <li>Mobile-first responsive</li>
                </ul>
            </div>
        </div>
        
        <div style="margin-bottom: 40px;">
            <h3 style="margin-bottom: 20px; font-size: 24px;">✅ What's New:</h3>
            <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                <p>🎨 <strong>Login Page:</strong> Full-screen gradient with floating animations</p>
                <p>📝 <strong>Register Page:</strong> Multi-step form with glass morphism</p>
                <p>🔒 <strong>Password Toggle:</strong> Interactive show/hide functionality</p>
                <p>✨ <strong>Sparkle Effects:</strong> Mouse-following particle animations</p>
                <p>📱 <strong>Mobile Optimized:</strong> Perfect responsive design</p>
                <p>🎯 <strong>Form Validation:</strong> Real-time feedback with animations</p>
            </div>
        </div>
        
        <div class="preview-buttons">
            <a href="auth/login.php" class="preview-btn preview-btn-primary">
                🔐 View New Login Page
            </a>
            <a href="auth/register.php" class="preview-btn preview-btn-secondary">
                📝 View New Register Page
            </a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.2);">
            <p style="font-size: 14px; opacity: 0.7;">
                🗑️ Delete this preview file after testing: <code>auth-preview.php</code>
            </p>
        </div>
    </div>
    
    <script>
        console.log('🔐 Auth pages redesign complete!');
        console.log('✨ Features: Gradients, glass morphism, animations, validation');
        console.log('🚀 Ready to impress users!');
        
        // Add some interactive sparkle effect
        document.addEventListener('mousemove', function(e) {
            if (Math.random() > 0.9) {
                const sparkle = document.createElement('div');
                sparkle.style.position = 'fixed';
                sparkle.style.left = e.clientX + 'px';
                sparkle.style.top = e.clientY + 'px';
                sparkle.style.width = '4px';
                sparkle.style.height = '4px';
                sparkle.style.background = 'white';
                sparkle.style.borderRadius = '50%';
                sparkle.style.pointerEvents = 'none';
                sparkle.style.zIndex = '1000';
                sparkle.style.animation = 'sparkle 1s ease-out forwards';
                document.body.appendChild(sparkle);
                
                setTimeout(() => {
                    sparkle.remove();
                }, 1000);
            }
        });
        
        // Add sparkle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkle {
                0% { opacity: 1; transform: scale(0); }
                50% { opacity: 1; transform: scale(1); }
                100% { opacity: 0; transform: scale(0); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
