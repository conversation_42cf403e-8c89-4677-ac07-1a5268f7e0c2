<?php
require_once '../config/config.php';

echo "<h1>🔧 Database Schema Update Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<h2>🔍 Checking Database Schema</h2>";
    
    // List of required columns and their definitions
    $requiredColumns = [
        'users' => [
            'status' => "ENUM('active', 'inactive', 'suspended') DEFAULT 'active'",
            'loyalty_points' => "INT DEFAULT 0",
            'is_verified' => "BOOLEAN DEFAULT FALSE"
        ],
        'quote_requests' => [
            'user_id' => "INT NULL",
            'product_id' => "INT NULL",
            'contact_name' => "VARCHAR(100) NOT NULL",
            'company_name' => "VARCHAR(100) NULL",
            'quantity' => "INT DEFAULT 1"
        ],
        'orders' => [
            'billing_first_name' => "VARCHAR(50) NULL",
            'billing_last_name' => "VARCHAR(50) NULL",
            'billing_email' => "VARCHAR(100) NULL",
            'billing_phone' => "VARCHAR(20) NULL",
            'billing_address' => "TEXT NULL",
            'billing_city' => "VARCHAR(50) NULL",
            'billing_state' => "VARCHAR(50) NULL",
            'billing_postal_code' => "VARCHAR(20) NULL",
            'billing_country' => "VARCHAR(50) NULL",
            'subtotal' => "DECIMAL(10,2) DEFAULT 0"
        ]
    ];
    
    $updates = [];
    $errors = [];
    
    foreach ($requiredColumns as $tableName => $columns) {
        echo "<h3>📋 Checking table: <code>$tableName</code></h3>";
        
        // Get existing columns
        $existingColumns = [];
        try {
            $stmt = $pdo->query("DESCRIBE $tableName");
            while ($row = $stmt->fetch()) {
                $existingColumns[] = $row['Field'];
            }
            echo "<div class='info'>✅ Table exists with " . count($existingColumns) . " columns</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Table $tableName does not exist: " . $e->getMessage() . "</div>";
            $errors[] = "Table $tableName missing";
            continue;
        }
        
        // Check each required column
        foreach ($columns as $columnName => $columnDef) {
            if (!in_array($columnName, $existingColumns)) {
                echo "<div class='warning'>⚠️ Missing column: <code>$columnName</code></div>";
                $updates[] = "ALTER TABLE $tableName ADD COLUMN $columnName $columnDef";
            } else {
                echo "<div class='success'>✅ Column exists: <code>$columnName</code></div>";
            }
        }
    }
    
    // Apply updates
    if (!empty($updates)) {
        echo "<h2>🔧 Applying Database Updates</h2>";
        echo "<div class='warning'><strong>⚠️ Warning:</strong> This will modify your database structure. Make sure you have a backup!</div>";
        
        foreach ($updates as $sql) {
            echo "<pre>$sql</pre>";
            try {
                $pdo->exec($sql);
                echo "<div class='success'>✅ Applied successfully</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ Failed: " . $e->getMessage() . "</div>";
                $errors[] = $sql;
            }
        }
        
        // Add foreign key constraints for new columns
        $foreignKeys = [
            "ALTER TABLE quote_requests ADD CONSTRAINT fk_quote_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL",
            "ALTER TABLE quote_requests ADD CONSTRAINT fk_quote_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL"
        ];
        
        echo "<h3>🔗 Adding Foreign Key Constraints</h3>";
        foreach ($foreignKeys as $sql) {
            echo "<pre>$sql</pre>";
            try {
                $pdo->exec($sql);
                echo "<div class='success'>✅ Foreign key added successfully</div>";
            } catch (Exception $e) {
                // Foreign keys might already exist or fail due to data issues
                echo "<div class='warning'>⚠️ Foreign key constraint skipped: " . $e->getMessage() . "</div>";
            }
        }
        
    } else {
        echo "<h2>🎉 Database Schema is Up to Date!</h2>";
        echo "<div class='success'>✅ All required columns exist. No updates needed.</div>";
    }
    
    // Update quote_requests table structure if needed
    echo "<h2>🔄 Updating Quote Requests Structure</h2>";
    
    // Check if we need to update existing quote_requests
    $quoteColumns = $pdo->query("DESCRIBE quote_requests")->fetchAll();
    $hasOldStructure = false;
    
    foreach ($quoteColumns as $col) {
        if ($col['Field'] === 'name' && !in_array('contact_name', array_column($quoteColumns, 'Field'))) {
            $hasOldStructure = true;
            break;
        }
    }
    
    if ($hasOldStructure) {
        echo "<div class='info'>🔄 Updating quote_requests table structure...</div>";
        try {
            // Rename 'name' to 'contact_name' if it exists
            $pdo->exec("ALTER TABLE quote_requests CHANGE name contact_name VARCHAR(100) NOT NULL");
            echo "<div class='success'>✅ Renamed 'name' column to 'contact_name'</div>";
            
            // Rename 'company' to 'company_name' if it exists
            $pdo->exec("ALTER TABLE quote_requests CHANGE company company_name VARCHAR(100) NULL");
            echo "<div class='success'>✅ Renamed 'company' column to 'company_name'</div>";
            
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ Column rename skipped: " . $e->getMessage() . "</div>";
        }
    }
    
    // Add missing constants to config if needed
    echo "<h2>⚙️ Configuration Check</h2>";
    
    if (!defined('ADMIN_ITEMS_PER_PAGE')) {
        echo "<div class='warning'>⚠️ Missing ADMIN_ITEMS_PER_PAGE constant</div>";
        echo "<div class='info'>💡 Add this to your config.php: <code>define('ADMIN_ITEMS_PER_PAGE', 20);</code></div>";
    } else {
        echo "<div class='success'>✅ ADMIN_ITEMS_PER_PAGE is defined</div>";
    }
    
    if (empty($errors)) {
        echo "<h2>🎉 Database Update Complete!</h2>";
        echo "<div class='success'>✅ Your database schema has been successfully updated!</div>";
        echo "<div class='info'>🔗 You can now access the admin dashboard: <a href='../admin/index.php'>Admin Dashboard</a></div>";
    } else {
        echo "<h2>⚠️ Some Issues Remain</h2>";
        echo "<div class='error'>❌ The following issues need manual attention:</div>";
        foreach ($errors as $error) {
            echo "<div class='error'>• $error</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database Error: " . $e->getMessage() . "</div>";
    echo "<h3>🔧 Manual Fix Instructions:</h3>";
    echo "<ol>";
    echo "<li>Access your database via phpMyAdmin or similar tool</li>";
    echo "<li>Run the following SQL commands manually:</li>";
    echo "</ol>";
    
    echo "<pre>";
    echo "-- Add missing columns to users table\n";
    echo "ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active';\n";
    echo "ALTER TABLE users ADD COLUMN loyalty_points INT DEFAULT 0;\n";
    echo "ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE;\n\n";
    
    echo "-- Add missing columns to quote_requests table\n";
    echo "ALTER TABLE quote_requests ADD COLUMN user_id INT NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN product_id INT NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN contact_name VARCHAR(100) NOT NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN company_name VARCHAR(100) NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN quantity INT DEFAULT 1;\n\n";
    
    echo "-- Add missing columns to orders table\n";
    echo "ALTER TABLE orders ADD COLUMN billing_first_name VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_last_name VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_email VARCHAR(100) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_phone VARCHAR(20) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_address TEXT NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_city VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_state VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_postal_code VARCHAR(20) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_country VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN subtotal DECIMAL(10,2) DEFAULT 0;\n";
    echo "</pre>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file (update-schema.php) after running it!";
echo "</p>";

echo "</div>";
?>
