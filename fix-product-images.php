<?php
require_once 'config/config.php';

echo "<h1>🖼️ Product Images Diagnostic & Fix Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; border: none; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
    .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f8f9fa; }
    .product-card img { width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 10px; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    
    // Check if we should fix images
    $fixImages = isset($_GET['fix']) && $_GET['fix'] === 'yes';
    
    echo "<h2>🔍 Diagnosing Product Images</h2>";
    
    // Check uploads directory
    $uploadsDir = ROOT_PATH . '/uploads/products/';
    echo "<h3>📁 Directory Check</h3>";
    
    if (!file_exists($uploadsDir)) {
        echo "<div class='error'>❌ Uploads directory does not exist: $uploadsDir</div>";
        mkdir($uploadsDir, 0755, true);
        echo "<div class='success'>✅ Created uploads directory</div>";
    } else {
        echo "<div class='success'>✅ Uploads directory exists: $uploadsDir</div>";
    }
    
    // Check directory permissions
    if (is_writable($uploadsDir)) {
        echo "<div class='success'>✅ Directory is writable</div>";
    } else {
        echo "<div class='error'>❌ Directory is not writable</div>";
    }
    
    // List files in uploads directory
    $files = glob($uploadsDir . '*');
    echo "<div class='info'>📂 Files in uploads directory: " . count($files) . "</div>";
    
    if (count($files) > 0) {
        echo "<h4>Files found:</h4>";
        echo "<ul>";
        foreach (array_slice($files, 0, 10) as $file) {
            $filename = basename($file);
            $size = filesize($file);
            echo "<li>$filename (" . number_format($size) . " bytes)</li>";
        }
        if (count($files) > 10) {
            echo "<li>... and " . (count($files) - 10) . " more files</li>";
        }
        echo "</ul>";
    }
    
    // Check database structure
    echo "<h3>🗄️ Database Check</h3>";
    
    // Check if product_images table exists
    try {
        $tables = $db->query("SHOW TABLES LIKE 'product_images'")->fetchAll();
        if (empty($tables)) {
            echo "<div class='error'>❌ product_images table does not exist</div>";
            
            // Create the table
            $createTable = "
                CREATE TABLE product_images (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    image_path VARCHAR(255) NOT NULL,
                    alt_text VARCHAR(255),
                    is_primary BOOLEAN DEFAULT FALSE,
                    sort_order INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                )
            ";
            
            if ($fixImages) {
                $db->query($createTable);
                echo "<div class='success'>✅ Created product_images table</div>";
            } else {
                echo "<div class='warning'>⚠️ Run with ?fix=yes to create the table</div>";
            }
        } else {
            echo "<div class='success'>✅ product_images table exists</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
    }
    
    // Check products and their images
    echo "<h3>🛍️ Products & Images Analysis</h3>";
    
    $products = $db->fetchAll("
        SELECT p.*, 
               pi.image_path as primary_image,
               COUNT(pi2.id) as total_images
        FROM products p 
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN product_images pi2 ON p.id = pi2.product_id
        GROUP BY p.id 
        ORDER BY p.id
    ");
    
    echo "<div class='info'>📊 Total products: " . count($products) . "</div>";
    
    $productsWithImages = 0;
    $productsWithoutImages = 0;
    $brokenImages = 0;
    
    foreach ($products as $product) {
        if ($product['primary_image']) {
            $productsWithImages++;
            $imagePath = $uploadsDir . $product['primary_image'];
            if (!file_exists($imagePath)) {
                $brokenImages++;
            }
        } else {
            $productsWithoutImages++;
        }
    }
    
    echo "<div class='success'>✅ Products with images: $productsWithImages</div>";
    echo "<div class='warning'>⚠️ Products without images: $productsWithoutImages</div>";
    echo "<div class='error'>❌ Products with broken image links: $brokenImages</div>";
    
    if ($fixImages && $productsWithoutImages > 0) {
        echo "<h3>🔧 Fixing Missing Images</h3>";
        
        // Add placeholder images or re-download images
        $placeholderImages = [
            'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1526947425960-945c6e72858f?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop'
        ];
        
        $imageIndex = 0;
        foreach ($products as $product) {
            if (!$product['primary_image']) {
                echo "<div class='info'>🔄 Adding image for: {$product['name']}</div>";
                
                $imageUrl = $placeholderImages[$imageIndex % count($placeholderImages)];
                $filename = $product['slug'] . '-main.jpg';
                $filepath = $uploadsDir . $filename;
                
                // Download image
                $imageData = @file_get_contents($imageUrl);
                if ($imageData && file_put_contents($filepath, $imageData)) {
                    // Add to database
                    $db->insert('product_images', [
                        'product_id' => $product['id'],
                        'image_path' => $filename,
                        'alt_text' => $product['name'],
                        'is_primary' => 1,
                        'sort_order' => 1
                    ]);
                    echo "<div class='success'>✅ Added image for {$product['name']}</div>";
                } else {
                    echo "<div class='error'>❌ Failed to download image for {$product['name']}</div>";
                }
                
                $imageIndex++;
            }
        }
    }
    
    // Show current products with images
    echo "<h3>🖼️ Current Products Preview</h3>";
    
    $productsPreview = $db->fetchAll("
        SELECT p.*, 
               pi.image_path as primary_image
        FROM products p 
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
        ORDER BY p.id 
        LIMIT 6
    ");
    
    echo "<div class='product-grid'>";
    foreach ($productsPreview as $product) {
        echo "<div class='product-card'>";
        echo "<h4>{$product['name']}</h4>";
        
        if ($product['primary_image']) {
            $imagePath = UPLOAD_URL . 'products/' . $product['primary_image'];
            $localPath = $uploadsDir . $product['primary_image'];
            
            if (file_exists($localPath)) {
                echo "<img src='$imagePath' alt='{$product['name']}' loading='lazy'>";
                echo "<div class='success'>✅ Image working</div>";
            } else {
                echo "<div style='height: 200px; background: #ddd; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-bottom: 10px;'>";
                echo "<span style='color: #666;'>Image file missing</span>";
                echo "</div>";
                echo "<div class='error'>❌ File not found: {$product['primary_image']}</div>";
            }
        } else {
            echo "<div style='height: 200px; background: #ddd; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-bottom: 10px;'>";
            echo "<span style='color: #666;'>No image</span>";
            echo "</div>";
            echo "<div class='warning'>⚠️ No image assigned</div>";
        }
        
        echo "<div><strong>Price:</strong> PGK " . number_format($product['price'], 2) . "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // Action buttons
    echo "<h3>🚀 Actions</h3>";
    
    if ($productsWithoutImages > 0 || $brokenImages > 0) {
        echo "<p><a href='?fix=yes' class='btn btn-success'>🔧 Fix Missing Images</a></p>";
    }
    
    echo "<p><a href='shop/products.php' class='btn'>🛍️ View Products Page</a></p>";
    echo "<p><a href='admin/products.php' class='btn'>⚙️ Admin Products</a></p>";
    
    // Configuration info
    echo "<h3>⚙️ Configuration</h3>";
    echo "<div class='info'>";
    echo "<strong>UPLOAD_PATH:</strong> " . (defined('UPLOAD_PATH') ? UPLOAD_PATH : 'Not defined') . "<br>";
    echo "<strong>UPLOAD_URL:</strong> " . (defined('UPLOAD_URL') ? UPLOAD_URL : 'Not defined') . "<br>";
    echo "<strong>ROOT_PATH:</strong> " . (defined('ROOT_PATH') ? ROOT_PATH : 'Not defined') . "<br>";
    echo "<strong>SITE_URL:</strong> " . (defined('SITE_URL') ? SITE_URL : 'Not defined') . "<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    
    echo "<h3>🔧 Manual Steps:</h3>";
    echo "<ol>";
    echo "<li>Check if uploads/products/ directory exists and is writable</li>";
    echo "<li>Verify product_images table exists in database</li>";
    echo "<li>Check if image files are actually saved in uploads/products/</li>";
    echo "<li>Verify UPLOAD_URL constant is correctly defined</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file after use!";
echo "</p>";

echo "</div>";
?>
