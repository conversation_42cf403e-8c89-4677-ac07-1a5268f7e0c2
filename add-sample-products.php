<?php
require_once 'config/config.php';

echo "<h1>🛍️ Sample Products Generator</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
    .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f8f9fa; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; border: none; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    
    // Check if we should add products
    $addProducts = isset($_GET['add']) && $_GET['add'] === 'yes';
    $clearProducts = isset($_GET['clear']) && $_GET['clear'] === 'yes';
    
    // Sample products data
    $sampleProducts = [
        [
            'name' => 'Premium Coffee Beans - Papua New Guinea',
            'description' => 'High-quality Arabica coffee beans grown in the highlands of Papua New Guinea. Rich, full-bodied flavor with hints of chocolate and caramel. Perfect for espresso or drip coffee.',
            'price' => 45.00,
            'sale_price' => 39.99,
            'stock_quantity' => 50,
            'category' => 'Food & Beverages',
            'sku' => 'PNG-COFFEE-001',
            'weight' => 1.0,
            'status' => 'active'
        ],
        [
            'name' => 'Traditional PNG Handicraft Mask',
            'description' => 'Authentic handcrafted wooden mask representing traditional Papua New Guinea culture. Made by local artisans using traditional techniques passed down through generations.',
            'price' => 120.00,
            'sale_price' => null,
            'stock_quantity' => 15,
            'category' => 'Arts & Crafts',
            'sku' => 'PNG-MASK-001',
            'weight' => 2.5,
            'status' => 'active'
        ],
        [
            'name' => 'Organic Coconut Oil - Cold Pressed',
            'description' => 'Pure, organic coconut oil cold-pressed from fresh coconuts. Perfect for cooking, skincare, and hair care. Rich in natural vitamins and antioxidants.',
            'price' => 25.00,
            'sale_price' => 22.50,
            'stock_quantity' => 75,
            'category' => 'Health & Beauty',
            'sku' => 'PNG-COCONUT-001',
            'weight' => 0.5,
            'status' => 'active'
        ],
        [
            'name' => 'PNG Sea Salt - Natural Harvest',
            'description' => 'Premium sea salt harvested from the pristine waters around Papua New Guinea. Unrefined and mineral-rich, perfect for gourmet cooking and seasoning.',
            'price' => 18.00,
            'sale_price' => null,
            'stock_quantity' => 100,
            'category' => 'Food & Beverages',
            'sku' => 'PNG-SALT-001',
            'weight' => 1.0,
            'status' => 'active'
        ],
        [
            'name' => 'Handwoven Traditional Bilum Bag',
            'description' => 'Beautiful traditional bilum bag handwoven by PNG women using natural fibers. Each bag is unique and represents hours of skilled craftsmanship. Perfect for shopping or as a cultural artifact.',
            'price' => 85.00,
            'sale_price' => 75.00,
            'stock_quantity' => 25,
            'category' => 'Fashion & Accessories',
            'sku' => 'PNG-BILUM-001',
            'weight' => 0.3,
            'status' => 'active'
        ],
        [
            'name' => 'PNG Vanilla Extract - Pure',
            'description' => 'Premium vanilla extract made from vanilla beans grown in Papua New Guinea. Intense, rich flavor perfect for baking and desserts. 100% natural with no artificial additives.',
            'price' => 35.00,
            'sale_price' => null,
            'stock_quantity' => 40,
            'category' => 'Food & Beverages',
            'sku' => 'PNG-VANILLA-001',
            'weight' => 0.25,
            'status' => 'active'
        ],
        [
            'name' => 'Traditional PNG Drums - Kundu',
            'description' => 'Authentic Kundu drum handcrafted by traditional PNG artisans. Made from natural materials including lizard skin and carved wood. Perfect for music enthusiasts and cultural collectors.',
            'price' => 200.00,
            'sale_price' => 180.00,
            'stock_quantity' => 8,
            'category' => 'Arts & Crafts',
            'sku' => 'PNG-DRUM-001',
            'weight' => 3.0,
            'status' => 'active'
        ],
        [
            'name' => 'PNG Honey - Wild Forest',
            'description' => 'Pure wild forest honey collected from the pristine rainforests of Papua New Guinea. Raw, unprocessed honey with unique floral notes and natural enzymes.',
            'price' => 28.00,
            'sale_price' => 25.20,
            'stock_quantity' => 60,
            'category' => 'Food & Beverages',
            'sku' => 'PNG-HONEY-001',
            'weight' => 0.5,
            'status' => 'active'
        ],
        [
            'name' => 'PNG Spice Mix - Traditional Blend',
            'description' => 'Authentic spice blend featuring traditional Papua New Guinea herbs and spices. Perfect for seasoning meats, vegetables, and traditional dishes. Adds unique PNG flavors to any meal.',
            'price' => 22.00,
            'sale_price' => null,
            'stock_quantity' => 80,
            'category' => 'Food & Beverages',
            'sku' => 'PNG-SPICE-001',
            'weight' => 0.2,
            'status' => 'active'
        ],
        [
            'name' => 'PNG Shell Jewelry Set',
            'description' => 'Beautiful jewelry set made from natural shells found on PNG beaches. Includes necklace, earrings, and bracelet. Each piece is unique and showcases the natural beauty of PNG marine life.',
            'price' => 65.00,
            'sale_price' => 55.00,
            'stock_quantity' => 20,
            'category' => 'Fashion & Accessories',
            'sku' => 'PNG-SHELL-001',
            'weight' => 0.1,
            'status' => 'active'
        ],
        [
            'name' => 'PNG Cocoa Powder - Premium Grade',
            'description' => 'High-quality cocoa powder made from PNG-grown cacao beans. Rich, intense chocolate flavor perfect for baking, hot chocolate, and desserts. Single-origin premium grade.',
            'price' => 32.00,
            'sale_price' => null,
            'stock_quantity' => 45,
            'category' => 'Food & Beverages',
            'sku' => 'PNG-COCOA-001',
            'weight' => 0.5,
            'status' => 'active'
        ],
        [
            'name' => 'Traditional PNG Tapa Cloth',
            'description' => 'Authentic tapa cloth made from bark using traditional PNG methods. Hand-painted with traditional designs and natural dyes. Perfect for wall decoration or cultural display.',
            'price' => 150.00,
            'sale_price' => 135.00,
            'stock_quantity' => 12,
            'category' => 'Arts & Crafts',
            'sku' => 'PNG-TAPA-001',
            'weight' => 1.0,
            'status' => 'active'
        ]
    ];
    
    if ($clearProducts) {
        echo "<h2>🗑️ Clearing Existing Products</h2>";
        
        // Clear existing products
        $db->query("DELETE FROM cart_items");
        $db->query("DELETE FROM order_items");
        $db->query("DELETE FROM product_images");
        $db->query("DELETE FROM products");
        $db->query("DELETE FROM categories");
        
        echo "<div class='success'>✅ All existing products, categories, and related data cleared!</div>";
        echo "<p><a href='?add=yes' class='btn btn-success'>🛍️ Add Sample Products</a></p>";
        
    } elseif ($addProducts) {
        echo "<h2>🛍️ Adding Sample Products</h2>";
        
        // First, create categories
        $categories = [
            'Food & Beverages' => 'Delicious food items and beverages from Papua New Guinea',
            'Arts & Crafts' => 'Traditional handcrafted items and cultural artifacts',
            'Health & Beauty' => 'Natural health and beauty products',
            'Fashion & Accessories' => 'Traditional and modern fashion items and accessories'
        ];
        
        echo "<h3>📂 Creating Categories</h3>";
        $categoryIds = [];
        
        foreach ($categories as $catName => $catDesc) {
            $slug = generate_slug($catName);
            
            // Check if category exists
            $existing = $db->fetch("SELECT id FROM categories WHERE slug = ?", [$slug]);
            
            if (!$existing) {
                $catId = $db->insert('categories', [
                    'name' => $catName,
                    'slug' => $slug,
                    'description' => $catDesc,
                    'is_active' => 1
                ]);
                $categoryIds[$catName] = $catId;
                echo "<div class='success'>✅ Created category: $catName</div>";
            } else {
                $categoryIds[$catName] = $existing['id'];
                echo "<div class='info'>ℹ️ Category already exists: $catName</div>";
            }
        }
        
        echo "<h3>🛍️ Adding Products</h3>";
        $productCount = 0;
        
        foreach ($sampleProducts as $product) {
            $slug = generate_slug($product['name']);
            
            // Check if product exists
            $existing = $db->fetch("SELECT id FROM products WHERE slug = ?", [$slug]);
            
            if (!$existing) {
                $productData = [
                    'name' => $product['name'],
                    'slug' => $slug,
                    'description' => $product['description'],
                    'price' => $product['price'],
                    'sale_price' => $product['sale_price'],
                    'sku' => $product['sku'],
                    'stock_quantity' => $product['stock_quantity'],
                    'manage_stock' => 1,
                    'weight' => $product['weight'],
                    'status' => $product['status'],
                    'category_id' => $categoryIds[$product['category']] ?? null,
                    'featured' => rand(0, 1) // Randomly make some products featured
                ];
                
                $productId = $db->insert('products', $productData);
                
                if ($productId) {
                    echo "<div class='success'>✅ Added: {$product['name']} (ID: $productId)</div>";
                    $productCount++;
                } else {
                    echo "<div class='error'>❌ Failed to add: {$product['name']}</div>";
                }
            } else {
                echo "<div class='info'>ℹ️ Product already exists: {$product['name']}</div>";
            }
        }
        
        echo "<h2>🎉 Sample Products Added Successfully!</h2>";
        echo "<div class='success'>✅ Added $productCount new products across " . count($categories) . " categories</div>";
        
        echo "<div class='warning'>";
        echo "<strong>📝 Note:</strong> Products have been added without images. You can add product images through the admin panel or they will show placeholder images.";
        echo "</div>";
        
        echo "<h3>🚀 Next Steps:</h3>";
        echo "<ul>";
        echo "<li><a href='shop/products.php' class='btn'>🛍️ View Products (Customer)</a></li>";
        echo "<li><a href='admin/products.php' class='btn'>⚙️ Manage Products (Admin)</a></li>";
        echo "<li><a href='admin/index.php' class='btn'>📊 Admin Dashboard</a></li>";
        echo "</ul>";
        
    } else {
        echo "<h2>🛍️ Sample Products Generator</h2>";
        echo "<p>This tool will add sample products to your store for testing purposes.</p>";
        
        // Check current products
        $currentProducts = $db->fetch("SELECT COUNT(*) as count FROM products")['count'];
        $currentCategories = $db->fetch("SELECT COUNT(*) as count FROM categories")['count'];
        
        echo "<h3>📊 Current Database Status:</h3>";
        echo "<div class='info'>Products: $currentProducts</div>";
        echo "<div class='info'>Categories: $currentCategories</div>";
        
        echo "<h3>🛍️ Sample Products to be Added:</h3>";
        echo "<div class='product-grid'>";
        foreach ($sampleProducts as $product) {
            echo "<div class='product-card'>";
            echo "<h4>{$product['name']}</h4>";
            echo "<p><strong>Category:</strong> {$product['category']}</p>";
            echo "<p><strong>Price:</strong> PGK " . number_format($product['price'], 2);
            if ($product['sale_price']) {
                echo " <span style='color: #dc3545;'>(Sale: PGK " . number_format($product['sale_price'], 2) . ")</span>";
            }
            echo "</p>";
            echo "<p><strong>Stock:</strong> {$product['stock_quantity']} units</p>";
            echo "<p style='font-size: 12px; color: #666;'>" . substr($product['description'], 0, 100) . "...</p>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<h3>⚠️ Choose an Action:</h3>";
        
        if ($currentProducts > 0) {
            echo "<div class='warning'>";
            echo "<strong>Warning:</strong> You already have $currentProducts products in your database. ";
            echo "You can either add more products or clear existing ones first.";
            echo "</div>";
            
            echo "<p><button onclick='if(confirm(\"This will DELETE all existing products, categories, cart items, and order items. Are you sure?\")) window.location.href=\"?clear=yes\"' class='btn btn-danger'>🗑️ Clear All Products First</button></p>";
        }
        
        echo "<p><a href='?add=yes' class='btn btn-success'>🛍️ Add Sample Products</a></p>";
        echo "<p><a href='admin/products.php' class='btn'>⚙️ Go to Admin Products</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    
    echo "<h3>🔧 Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure your database schema is up to date</li>";
    echo "<li>Check that all required tables exist (products, categories, etc.)</li>";
    echo "<li>Verify database connection settings</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file (add-sample-products.php) after use!";
echo "</p>";

echo "</div>";
?>
