<?php
require_once 'config/config.php';

echo "<h1>🛍️ Safe Sample Products Generator</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; border: none; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    $pdo = $db->getConnection();
    
    // First, let's check what columns exist in the products table
    echo "<h2>🔍 Checking Products Table Structure</h2>";
    
    $columns = $pdo->query("DESCRIBE products")->fetchAll();
    $availableColumns = array_column($columns, 'Field');
    
    echo "<div class='info'>Available columns in products table:</div>";
    echo "<pre>" . implode(', ', $availableColumns) . "</pre>";
    
    // Check if we should add products
    $addProducts = isset($_GET['add']) && $_GET['add'] === 'yes';
    
    if ($addProducts) {
        echo "<h2>🛍️ Adding Sample Products</h2>";
        
        // Create categories first
        $categories = [
            'Food & Beverages' => 'Delicious food items and beverages from Papua New Guinea',
            'Arts & Crafts' => 'Traditional handcrafted items and cultural artifacts',
            'Health & Beauty' => 'Natural health and beauty products',
            'Fashion & Accessories' => 'Traditional and modern fashion items and accessories'
        ];
        
        echo "<h3>📂 Creating Categories</h3>";
        $categoryIds = [];
        
        foreach ($categories as $catName => $catDesc) {
            $slug = generate_slug($catName);
            
            // Check if category exists
            $existing = $db->fetch("SELECT id FROM categories WHERE slug = ?", [$slug]);
            
            if (!$existing) {
                $catId = $db->insert('categories', [
                    'name' => $catName,
                    'slug' => $slug,
                    'description' => $catDesc,
                    'is_active' => 1
                ]);
                $categoryIds[$catName] = $catId;
                echo "<div class='success'>✅ Created category: $catName</div>";
            } else {
                $categoryIds[$catName] = $existing['id'];
                echo "<div class='info'>ℹ️ Category already exists: $catName</div>";
            }
        }
        
        // Sample products with only basic fields
        $sampleProducts = [
            [
                'name' => 'Premium Coffee Beans - Papua New Guinea',
                'description' => 'High-quality Arabica coffee beans grown in the highlands of Papua New Guinea. Rich, full-bodied flavor with hints of chocolate and caramel.',
                'price' => 45.00,
                'sale_price' => 39.99,
                'stock_quantity' => 50,
                'category' => 'Food & Beverages',
                'sku' => 'PNG-COFFEE-001'
            ],
            [
                'name' => 'Traditional PNG Handicraft Mask',
                'description' => 'Authentic handcrafted wooden mask representing traditional Papua New Guinea culture. Made by local artisans.',
                'price' => 120.00,
                'sale_price' => null,
                'stock_quantity' => 15,
                'category' => 'Arts & Crafts',
                'sku' => 'PNG-MASK-001'
            ],
            [
                'name' => 'Organic Coconut Oil - Cold Pressed',
                'description' => 'Pure, organic coconut oil cold-pressed from fresh coconuts. Perfect for cooking, skincare, and hair care.',
                'price' => 25.00,
                'sale_price' => 22.50,
                'stock_quantity' => 75,
                'category' => 'Health & Beauty',
                'sku' => 'PNG-COCONUT-001'
            ],
            [
                'name' => 'PNG Sea Salt - Natural Harvest',
                'description' => 'Premium sea salt harvested from the pristine waters around Papua New Guinea. Unrefined and mineral-rich.',
                'price' => 18.00,
                'sale_price' => null,
                'stock_quantity' => 100,
                'category' => 'Food & Beverages',
                'sku' => 'PNG-SALT-001'
            ],
            [
                'name' => 'Handwoven Traditional Bilum Bag',
                'description' => 'Beautiful traditional bilum bag handwoven by PNG women using natural fibers. Each bag is unique.',
                'price' => 85.00,
                'sale_price' => 75.00,
                'stock_quantity' => 25,
                'category' => 'Fashion & Accessories',
                'sku' => 'PNG-BILUM-001'
            ],
            [
                'name' => 'PNG Vanilla Extract - Pure',
                'description' => 'Premium vanilla extract made from vanilla beans grown in Papua New Guinea. Intense, rich flavor.',
                'price' => 35.00,
                'sale_price' => null,
                'stock_quantity' => 40,
                'category' => 'Food & Beverages',
                'sku' => 'PNG-VANILLA-001'
            ],
            [
                'name' => 'Traditional PNG Drums - Kundu',
                'description' => 'Authentic Kundu drum handcrafted by traditional PNG artisans. Made from natural materials.',
                'price' => 200.00,
                'sale_price' => 180.00,
                'stock_quantity' => 8,
                'category' => 'Arts & Crafts',
                'sku' => 'PNG-DRUM-001'
            ],
            [
                'name' => 'PNG Honey - Wild Forest',
                'description' => 'Pure wild forest honey collected from the pristine rainforests of Papua New Guinea.',
                'price' => 28.00,
                'sale_price' => 25.20,
                'stock_quantity' => 60,
                'category' => 'Food & Beverages',
                'sku' => 'PNG-HONEY-001'
            ],
            [
                'name' => 'PNG Spice Mix - Traditional Blend',
                'description' => 'Authentic spice blend featuring traditional Papua New Guinea herbs and spices.',
                'price' => 22.00,
                'sale_price' => null,
                'stock_quantity' => 80,
                'category' => 'Food & Beverages',
                'sku' => 'PNG-SPICE-001'
            ],
            [
                'name' => 'PNG Shell Jewelry Set',
                'description' => 'Beautiful jewelry set made from natural shells found on PNG beaches. Includes necklace, earrings, and bracelet.',
                'price' => 65.00,
                'sale_price' => 55.00,
                'stock_quantity' => 20,
                'category' => 'Fashion & Accessories',
                'sku' => 'PNG-SHELL-001'
            ]
        ];
        
        echo "<h3>🛍️ Adding Products</h3>";
        $productCount = 0;
        
        foreach ($sampleProducts as $product) {
            $slug = generate_slug($product['name']);
            
            // Check if product exists
            $existing = $db->fetch("SELECT id FROM products WHERE slug = ?", [$slug]);
            
            if (!$existing) {
                // Build product data with only available columns
                $productData = [
                    'name' => $product['name'],
                    'slug' => $slug,
                    'description' => $product['description'],
                    'price' => $product['price'],
                    'stock_quantity' => $product['stock_quantity'],
                    'category_id' => $categoryIds[$product['category']] ?? null,
                    'status' => 'active'
                ];
                
                // Add optional columns only if they exist
                if (in_array('sale_price', $availableColumns)) {
                    $productData['sale_price'] = $product['sale_price'];
                }
                
                if (in_array('sku', $availableColumns)) {
                    $productData['sku'] = $product['sku'];
                }
                
                if (in_array('manage_stock', $availableColumns)) {
                    $productData['manage_stock'] = 1;
                }
                
                if (in_array('featured', $availableColumns)) {
                    $productData['featured'] = rand(0, 1);
                }
                
                if (in_array('weight', $availableColumns)) {
                    $productData['weight'] = 1.0;
                }
                
                $productId = $db->insert('products', $productData);
                
                if ($productId) {
                    echo "<div class='success'>✅ Added: {$product['name']} (ID: $productId)</div>";
                    $productCount++;
                } else {
                    echo "<div class='error'>❌ Failed to add: {$product['name']}</div>";
                }
            } else {
                echo "<div class='info'>ℹ️ Product already exists: {$product['name']}</div>";
            }
        }
        
        echo "<h2>🎉 Sample Products Added Successfully!</h2>";
        echo "<div class='success'>✅ Added $productCount new products across " . count($categories) . " categories</div>";
        
        echo "<h3>🚀 Test Your Store Now:</h3>";
        echo "<ul>";
        echo "<li><a href='shop/products.php' class='btn'>🛍️ View Products (Customer)</a></li>";
        echo "<li><a href='admin/products.php' class='btn'>⚙️ Manage Products (Admin)</a></li>";
        echo "<li><a href='admin/index.php' class='btn'>📊 Admin Dashboard</a></li>";
        echo "</ul>";
        
        echo "<h3>🧪 Test Cart Functionality:</h3>";
        echo "<ol>";
        echo "<li>Go to the products page and add items to cart</li>";
        echo "<li>View your cart at <a href='cart.php'>cart.php</a></li>";
        echo "<li>Register/login and complete checkout</li>";
        echo "<li>Check admin dashboard for new orders</li>";
        echo "</ol>";
        
    } else {
        echo "<h2>🛍️ Ready to Add Sample Products</h2>";
        echo "<p>This will add 10 sample products across 4 categories to test your e-commerce functionality.</p>";
        
        // Check current products
        $currentProducts = $db->fetch("SELECT COUNT(*) as count FROM products")['count'];
        echo "<div class='info'>Current products in database: $currentProducts</div>";
        
        echo "<h3>✨ What will be added:</h3>";
        echo "<ul>";
        echo "<li><strong>4 Categories:</strong> Food & Beverages, Arts & Crafts, Health & Beauty, Fashion & Accessories</li>";
        echo "<li><strong>10 Products:</strong> PNG-themed products with realistic pricing</li>";
        echo "<li><strong>Stock levels:</strong> Varying quantities for testing</li>";
        echo "<li><strong>Sale prices:</strong> Some products will have discounted prices</li>";
        echo "</ul>";
        
        echo "<p><a href='?add=yes' class='btn btn-success'>🛍️ Add Sample Products Now</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    
    echo "<h3>🔧 Manual Product Addition:</h3>";
    echo "<p>If the automatic method fails, you can add products manually through the admin panel:</p>";
    echo "<ol>";
    echo "<li>Go to <a href='admin/products.php'>Admin Products</a></li>";
    echo "<li>Click 'Add New Product'</li>";
    echo "<li>Fill in the product details</li>";
    echo "<li>Save the product</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file after use!";
echo "</p>";

echo "</div>";
?>
