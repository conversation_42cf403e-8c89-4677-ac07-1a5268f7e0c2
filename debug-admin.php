<?php
require_once 'config/config.php';

echo "<h2>🔍 Admin Login Debug Tool</h2>";
echo "<style>body{font-family:Arial;padding:20px;background:#f5f5f5;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#fff;padding:15px;border-radius:5px;}</style>";

try {
    echo "<h3>1. Database Connection Test</h3>";
    $db = GeusGalore\Database::getInstance();
    echo "<div class='success'>✅ Database connection successful!</div>";
    
    echo "<h3>2. Check if admin_users table exists</h3>";
    $tableExists = $db->fetch("SHOW TABLES LIKE 'admin_users'");
    if ($tableExists) {
        echo "<div class='success'>✅ admin_users table exists</div>";
        
        echo "<h3>3. Check admin users in database</h3>";
        $adminUsers = $db->fetchAll("SELECT id, username, email, role, created_at FROM admin_users");
        
        if (empty($adminUsers)) {
            echo "<div class='error'>❌ No admin users found in database!</div>";
            echo "<h4>Creating default admin user...</h4>";
            
            // Create default admin user
            $username = 'admin';
            $email = '<EMAIL>';
            $password = 'admin123';
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            $result = $db->insert('admin_users', [
                'username' => $username,
                'email' => $email,
                'password' => $hashedPassword,
                'role' => 'super_admin'
            ]);
            
            if ($result) {
                echo "<div class='success'>✅ Default admin user created successfully!</div>";
                echo "<div class='info'>📝 Login with: username='admin', password='admin123'</div>";
            } else {
                echo "<div class='error'>❌ Failed to create admin user</div>";
            }
        } else {
            echo "<div class='success'>✅ Found " . count($adminUsers) . " admin user(s):</div>";
            echo "<pre>";
            foreach ($adminUsers as $user) {
                echo "ID: {$user['id']}\n";
                echo "Username: {$user['username']}\n";
                echo "Email: {$user['email']}\n";
                echo "Role: {$user['role']}\n";
                echo "Created: {$user['created_at']}\n";
                echo "---\n";
            }
            echo "</pre>";
        }
        
        echo "<h3>4. Test password verification</h3>";
        $testAdmin = $db->fetch("SELECT * FROM admin_users WHERE username = 'admin' OR email = '<EMAIL>'");
        
        if ($testAdmin) {
            echo "<div class='success'>✅ Admin user found in database</div>";
            
            // Test password verification
            $testPassword = 'admin123';
            $passwordValid = password_verify($testPassword, $testAdmin['password']);
            
            if ($passwordValid) {
                echo "<div class='success'>✅ Password verification successful!</div>";
                echo "<div class='info'>🔑 You can login with:</div>";
                echo "<div class='info'>Username: admin (or <EMAIL>)</div>";
                echo "<div class='info'>Password: admin123</div>";
            } else {
                echo "<div class='error'>❌ Password verification failed!</div>";
                echo "<div class='info'>🔧 Updating password hash...</div>";
                
                // Update password with new hash
                $newHash = password_hash('admin123', PASSWORD_DEFAULT);
                $updateResult = $db->update('admin_users',
                    ['password' => $newHash],
                    'id = ?',
                    [$testAdmin['id']]
                );
                
                if ($updateResult) {
                    echo "<div class='success'>✅ Password updated successfully!</div>";
                    echo "<div class='info'>🔑 Try logging in again with: username='admin', password='admin123'</div>";
                } else {
                    echo "<div class='error'>❌ Failed to update password</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ No admin user found with username 'admin'</div>";
        }
        
    } else {
        echo "<div class='error'>❌ admin_users table does not exist!</div>";
        echo "<div class='info'>📋 You need to import the database schema first:</div>";
        echo "<div class='info'>Import: database/schema.sql</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database Error: " . $e->getMessage() . "</div>";
    echo "<h3>Possible Solutions:</h3>";
    echo "<ul>";
    echo "<li>Check database connection settings in config/config.php</li>";
    echo "<li>Make sure database exists and is accessible</li>";
    echo "<li>Import database/schema.sql if tables don't exist</li>";
    echo "<li>Check database user permissions</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>5. Quick Actions</h3>";
echo "<p><a href='admin/login.php' style='background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🔐 Try Admin Login</a></p>";
echo "<p><a href='index.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🏠 Back to Homepage</a></p>";
echo "<p style='color:#666;font-size:12px;'>⚠️ Delete this file (debug-admin.php) after fixing the issue for security!</p>";
?>
