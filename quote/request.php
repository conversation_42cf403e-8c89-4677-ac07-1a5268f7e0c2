<?php
require_once '../config/config.php';

$pageTitle = 'Request Quote';
$pageDescription = 'Get a personalized quote for your product needs at Geu\'s Galore. Fast, accurate, and competitive pricing.';

// Get product if specified
$selectedProduct = null;
if (isset($_GET['product'])) {
    $productId = intval($_GET['product']);
    $product = new GeusGalore\Product();
    $selectedProduct = $product->getProductById($productId);
}

// Get all products for selection
$product = new GeusGalore\Product();
$allProducts = $product->getAllProducts(1, 100); // Get first 100 products

$success = false;
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form data
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $company = sanitize_input($_POST['company'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    $products = $_POST['products'] ?? [];
    
    // Validation
    if (empty($name)) {
        $errors['name'] = 'Name is required';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Valid email is required';
    }
    
    if (empty($products)) {
        $errors['products'] = 'Please select at least one product';
    }
    
    if (empty($errors)) {
        try {
            $db = GeusGalore\Database::getInstance();
            
            // Generate quote number
            $quoteNumber = 'QT' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Insert quote request
            $quoteData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'company' => $company,
                'message' => $message,
                'quote_number' => $quoteNumber,
                'valid_until' => date('Y-m-d', strtotime('+30 days'))
            ];
            
            $quoteId = $db->insert('quote_requests', $quoteData);
            
            // Insert quote items
            $totalAmount = 0;
            foreach ($products as $productData) {
                $productId = intval($productData['id']);
                $quantity = intval($productData['quantity']);
                
                if ($productId > 0 && $quantity > 0) {
                    $productInfo = $product->getProductById($productId);
                    if ($productInfo) {
                        $price = $productInfo['sale_price'] ?: $productInfo['price'];
                        $itemTotal = $price * $quantity;
                        $totalAmount += $itemTotal;
                        
                        $db->insert('quote_items', [
                            'quote_id' => $quoteId,
                            'product_id' => $productId,
                            'quantity' => $quantity,
                            'price' => $price,
                            'total' => $itemTotal
                        ]);
                    }
                }
            }
            
            // Update quote with total amount
            $db->update('quote_requests', ['total_amount' => $totalAmount], 'id = ?', [$quoteId]);
            
            // Send email notification
            $emailSender = new GeusGalore\Email();
            $emailSender->send(
                ADMIN_EMAIL,
                "New Quote Request - {$quoteNumber}",
                "A new quote request has been submitted by {$name} ({$email}). Quote Number: {$quoteNumber}"
            );
            
            $success = true;
            flash_message('success', 'Quote request submitted successfully! We will contact you within 24 hours.');
            
        } catch (Exception $e) {
            error_log('Quote request error: ' . $e->getMessage());
            $errors['general'] = 'Failed to submit quote request. Please try again.';
        }
    }
}

include '../includes/header.php';
?>

<!-- Epic Page Header -->
<section style="padding: 120px 0; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ffd93d 100%); position: relative; overflow: hidden;">
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center;">
            <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                <i class="fas fa-file-invoice-dollar"></i>
                Get Your Quote
            </div>
            <h1 style="font-size: clamp(2.5rem, 6vw, 4.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                Request a <span style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Quote</span>
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 700px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                Get personalized pricing for your product needs. Fill out the form below and we'll get back to you within 24 hours.
            </p>
        </div>
    </div>
</section>

<div class="container" style="max-width: 800px; margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">
    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 15px 35px rgba(0,0,0,0.1);">
        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb); border-radius: 30px 30px 0 0;"></div>

        <?php if ($success): ?>
            <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 30px; border-radius: 20px; text-align: center; margin-bottom: 30px; box-shadow: 0 15px 35px rgba(67,233,123,0.3);">
                <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 20px; text-shadow: 0 0 20px rgba(255,255,255,0.5);"></i>
                <h3 style="font-size: 28px; font-weight: 800; margin-bottom: 15px;">Quote Request Submitted!</h3>
                <p style="font-size: 18px; font-weight: 500; opacity: 0.9;">Thank you for your interest. We'll review your request and send you a detailed quote within 24 hours.</p>
            </div>
            <div style="text-align: center;">
                <a href="<?php echo SITE_URL; ?>/shop/products.php"
                   style="display: inline-flex; align-items: center; gap: 12px; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%); color: white; padding: 20px 40px; border-radius: 50px; text-decoration: none; font-weight: 700; font-size: 18px; transition: all 0.4s ease; box-shadow: 0 15px 35px rgba(255,107,107,0.4);"
                   onmouseover="this.style.transform='translateY(-5px) scale(1.05)'; this.style.boxShadow='0 25px 50px rgba(255,107,107,0.6)'"
                   onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 15px 35px rgba(255,107,107,0.4)'">
                    <i class="fas fa-shopping-bag"></i>
                    Continue Shopping
                </a>
            </div>
        <?php else: ?>
            <form method="POST" id="quoteForm">
                <!-- Personal Information -->
                <h3 style="color: var(--dark-brown); margin-bottom: 20px; border-bottom: 2px solid var(--cream); padding-bottom: 10px;">
                    Contact Information
                </h3>
                
                <div class="row">
                    <div class="col col-6">
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                            <?php if (isset($errors['name'])): ?>
                                <div class="error-message"><?php echo $errors['name']; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col col-6">
                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                            <?php if (isset($errors['email'])): ?>
                                <div class="error-message"><?php echo $errors['email']; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col col-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                   placeholder="+************">
                        </div>
                    </div>
                    <div class="col col-6">
                        <div class="form-group">
                            <label for="company" class="form-label">Company (Optional)</label>
                            <input type="text" id="company" name="company" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['company'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Product Selection -->
                <h3 style="color: var(--dark-brown); margin: 40px 0 20px; border-bottom: 2px solid var(--cream); padding-bottom: 10px;">
                    Product Selection
                </h3>
                
                <div id="productSelection">
                    <?php if ($selectedProduct): ?>
                        <div class="product-item" style="background-color: var(--cream); padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                            <div style="display: flex; align-items: center; gap: 20px;">
                                <?php if ($selectedProduct['images'] && !empty($selectedProduct['images'])): ?>
                                    <img src="<?php echo UPLOAD_URL . 'products/' . $selectedProduct['images'][0]['image_path']; ?>" 
                                         alt="<?php echo htmlspecialchars($selectedProduct['name']); ?>"
                                         style="width: 80px; height: 80px; object-fit: cover; border-radius: 5px;">
                                <?php endif; ?>
                                <div style="flex: 1;">
                                    <h4 style="color: var(--dark-brown); margin-bottom: 5px;">
                                        <?php echo htmlspecialchars($selectedProduct['name']); ?>
                                    </h4>
                                    <p style="color: var(--light-brown); margin-bottom: 10px;">
                                        <?php echo format_currency($selectedProduct['sale_price'] ?: $selectedProduct['price']); ?>
                                    </p>
                                    <input type="hidden" name="products[0][id]" value="<?php echo $selectedProduct['id']; ?>">
                                    <label>Quantity: 
                                        <input type="number" name="products[0][quantity]" value="1" min="1" 
                                               style="width: 80px; padding: 5px; margin-left: 10px;">
                                    </label>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div style="margin-bottom: 20px;">
                        <button type="button" id="addProduct" class="btn btn-outline">
                            <i class="fas fa-plus"></i> Add Another Product
                        </button>
                    </div>
                </div>
                
                <?php if (isset($errors['products'])): ?>
                    <div class="error-message" style="margin-bottom: 20px;"><?php echo $errors['products']; ?></div>
                <?php endif; ?>
                
                <!-- Additional Information -->
                <h3 style="color: var(--dark-brown); margin: 40px 0 20px; border-bottom: 2px solid var(--cream); padding-bottom: 10px;">
                    Additional Information
                </h3>
                
                <div class="form-group">
                    <label for="message" class="form-label">Message (Optional)</label>
                    <textarea id="message" name="message" class="form-control" rows="4" 
                              placeholder="Tell us about your specific requirements, delivery preferences, or any questions you have..."><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                </div>
                
                <?php if (isset($errors['general'])): ?>
                    <div style="background-color: var(--danger); color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <?php echo $errors['general']; ?>
                    </div>
                <?php endif; ?>
                
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    <i class="fas fa-paper-plane"></i> Submit Quote Request
                </button>
            </form>
        <?php endif; ?>
    </div>
</div>

<!-- Product Selection Modal -->
<div id="productModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 10000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
        <h3 style="margin-bottom: 20px; color: var(--dark-brown);">Select a Product</h3>
        <div id="productList">
            <?php foreach ($allProducts['products'] as $prod): ?>
                <div class="product-option" data-id="<?php echo $prod['id']; ?>" 
                     data-name="<?php echo htmlspecialchars($prod['name']); ?>"
                     data-price="<?php echo $prod['sale_price'] ?: $prod['price']; ?>"
                     data-image="<?php echo $prod['primary_image'] ? UPLOAD_URL . 'products/' . $prod['primary_image'] : ''; ?>"
                     style="display: flex; align-items: center; gap: 15px; padding: 15px; border: 1px solid var(--cream); margin-bottom: 10px; cursor: pointer; border-radius: 5px;">
                    <?php if ($prod['primary_image']): ?>
                        <img src="<?php echo UPLOAD_URL . 'products/' . $prod['primary_image']; ?>" 
                             alt="<?php echo htmlspecialchars($prod['name']); ?>"
                             style="width: 50px; height: 50px; object-fit: cover; border-radius: 3px;">
                    <?php else: ?>
                        <div style="width: 50px; height: 50px; background-color: var(--cream); border-radius: 3px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-image" style="color: var(--light-brown);"></i>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h4 style="margin: 0; color: var(--dark-brown);"><?php echo htmlspecialchars($prod['name']); ?></h4>
                        <p style="margin: 0; color: var(--light-brown);"><?php echo format_currency($prod['sale_price'] ?: $prod['price']); ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <button type="button" id="closeModal" class="btn btn-secondary">Cancel</button>
        </div>
    </div>
</div>

<script>
let productIndex = <?php echo $selectedProduct ? 1 : 0; ?>;

document.getElementById('addProduct').addEventListener('click', function() {
    document.getElementById('productModal').style.display = 'block';
});

document.getElementById('closeModal').addEventListener('click', function() {
    document.getElementById('productModal').style.display = 'none';
});

document.querySelectorAll('.product-option').forEach(option => {
    option.addEventListener('click', function() {
        const id = this.dataset.id;
        const name = this.dataset.name;
        const price = this.dataset.price;
        const image = this.dataset.image;
        
        addProductToSelection(id, name, price, image);
        document.getElementById('productModal').style.display = 'none';
    });
});

function addProductToSelection(id, name, price, image) {
    const productHtml = `
        <div class="product-item" style="background-color: var(--cream); padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; gap: 20px;">
                ${image ? `<img src="${image}" alt="${name}" style="width: 80px; height: 80px; object-fit: cover; border-radius: 5px;">` : ''}
                <div style="flex: 1;">
                    <h4 style="color: var(--dark-brown); margin-bottom: 5px;">${name}</h4>
                    <p style="color: var(--light-brown); margin-bottom: 10px;">PGK ${parseFloat(price).toFixed(2)}</p>
                    <input type="hidden" name="products[${productIndex}][id]" value="${id}">
                    <label>Quantity: 
                        <input type="number" name="products[${productIndex}][quantity]" value="1" min="1" 
                               style="width: 80px; padding: 5px; margin-left: 10px;">
                    </label>
                </div>
                <button type="button" class="btn btn-secondary" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    const addButton = document.getElementById('addProduct');
    addButton.insertAdjacentHTML('beforebegin', productHtml);
    productIndex++;
}
</script>

<style>
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .col-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    section {
        padding: 60px 0 !important;
    }

    div[style*="padding: 40px"] {
        padding: 25px !important;
    }

    div[style*="onmouseover"] {
        transform: none !important;
        transition: none !important;
    }

    div[style*="onmouseover"]:hover {
        transform: none !important;
    }
}

@media (max-width: 480px) {
    section {
        padding: 40px 0 !important;
    }

    div[style*="padding: 25px"] {
        padding: 20px !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
