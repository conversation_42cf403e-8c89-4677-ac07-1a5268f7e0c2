<?php
require_once '../config/config.php';

$pageTitle = 'Our Vision';
$pageDescription = 'Discover the vision that drives Geu\'s Galore - to become Papua New Guinea\'s leading e-commerce platform and empower local communities.';

include '../includes/header.php';
?>

<!-- Epic Page Header -->
<section style="padding: 120px 0; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%); position: relative; overflow: hidden;">
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center;">
            <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                <i class="fas fa-eye"></i>
                Our Vision
            </div>
            <h1 style="font-size: clamp(2.5rem, 6vw, 4.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                Transforming <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Papua New Guinea</span>
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 800px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                To transform Papua New Guinea's retail landscape through innovative e-commerce solutions,
                connecting communities and empowering economic growth across the nation
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">

    <!-- Vision Statement -->
    <section style="padding: 80px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); position: relative; overflow: hidden; border-radius: 30px; margin-bottom: 80px;">
        <div style="position: relative; z-index: 10; text-align: center; padding: 0 40px;">
            <div style="max-width: 900px; margin: 0 auto;">
                <div style="width: 120px; height: 120px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); border-radius: 30px; margin: 0 auto 40px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(255,255,255,0.2); position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);">
                    <i class="fas fa-eye" style="font-size: 50px; color: white; z-index: 2; position: relative;"></i>
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                </div>
                <h2 style="color: white; margin-bottom: 30px; font-size: clamp(1.8rem, 4vw, 2.5rem); font-weight: 800; line-height: 1.3; text-shadow: 0 0 20px rgba(255,255,255,0.5);">
                    "To be Papua New Guinea's most trusted and innovative e-commerce platform,
                    bridging the gap between quality products and every household across our beautiful nation."
                </h2>
                <p style="color: rgba(255,255,255,0.9); font-size: clamp(1.1rem, 2.5vw, 1.3rem); line-height: 1.8; font-weight: 500;">
                    We envision a future where every Papua New Guinean has access to quality products,
                    competitive prices, and exceptional service, regardless of their location. Through technology
                    and innovation, we aim to create opportunities, support local businesses, and contribute
                    to the economic development of our communities.
                </p>
            </div>
        </div>
    </section>

    <!-- Vision Pillars -->
    <section style="padding: 80px 0; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ffd93d 100%); position: relative; overflow: hidden; border-radius: 30px; margin-bottom: 80px;">
        <div style="position: relative; z-index: 10; padding: 0 40px;">
            <div style="text-align: center; margin-bottom: 60px;">
                <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                    <i class="fas fa-columns"></i>
                    Vision Pillars
                </div>
                <h2 style="font-size: clamp(2.5rem, 5vw, 3.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                    The <span style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Foundation</span> of Our Vision
                </h2>
            </div>
            <div class="row">
                <div class="col col-4" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-globe" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Accessibility</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Making quality products accessible to every corner of Papua New Guinea,
                            from urban centers to remote villages, through innovative delivery solutions
                            and digital connectivity.
                        </p>
                    </div>
                </div>
                <div class="col col-4" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-rocket" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Innovation</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Continuously evolving our platform with cutting-edge technology,
                            user-friendly interfaces, and innovative features that enhance
                            the shopping experience for all Papua New Guineans.
                        </p>
                    </div>
                </div>
                <div class="col col-4" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-users" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Community</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Building strong relationships with local suppliers, supporting
                            small businesses, and creating employment opportunities that
                            contribute to the growth of Papua New Guinea's economy.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Future Goals -->
    <div style="background-color: var(--cream); padding: 60px 40px; border-radius: 15px; margin-bottom: 60px;">
        <h2 style="text-align: center; color: var(--dark-brown); margin-bottom: 40px;">
            Looking Ahead: Our 2030 Goals
        </h2>
        <div class="row">
            <div class="col col-6">
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background-color: var(--dark-brown); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                            <i class="fas fa-map-marked-alt" style="color: var(--white);"></i>
                        </div>
                        <h3 style="color: var(--dark-brown);">Nationwide Coverage</h3>
                    </div>
                    <p style="color: var(--light-brown); line-height: 1.6;">
                        Establish delivery networks reaching every province and district in Papua New Guinea, 
                        ensuring no community is left behind in the digital commerce revolution.
                    </p>
                </div>
                
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background-color: var(--dark-brown); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                            <i class="fas fa-store" style="color: var(--white);"></i>
                        </div>
                        <h3 style="color: var(--dark-brown);">Local Marketplace</h3>
                    </div>
                    <p style="color: var(--light-brown); line-height: 1.6;">
                        Create a thriving marketplace where local artisans, farmers, and small businesses 
                        can showcase and sell their products to customers across the country and beyond.
                    </p>
                </div>
            </div>
            <div class="col col-6">
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background-color: var(--dark-brown); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                            <i class="fas fa-graduation-cap" style="color: var(--white);"></i>
                        </div>
                        <h3 style="color: var(--dark-brown);">Digital Literacy</h3>
                    </div>
                    <p style="color: var(--light-brown); line-height: 1.6;">
                        Partner with educational institutions and community organizations to promote 
                        digital literacy and e-commerce skills across Papua New Guinea.
                    </p>
                </div>
                
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background-color: var(--dark-brown); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                            <i class="fas fa-leaf" style="color: var(--white);"></i>
                        </div>
                        <h3 style="color: var(--dark-brown);">Sustainability</h3>
                    </div>
                    <p style="color: var(--light-brown); line-height: 1.6;">
                        Implement environmentally sustainable practices in all operations, 
                        from packaging to delivery, while promoting eco-friendly products and practices.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Call to Action -->
    <div style="background-color: var(--dark-brown); color: var(--white); padding: 60px 40px; border-radius: 15px; text-align: center;">
        <h2 style="margin-bottom: 20px;">Join Us in Building the Future</h2>
        <p style="margin-bottom: 30px; color: var(--cream); font-size: 18px; max-width: 600px; margin-left: auto; margin-right: auto;">
            Our vision becomes reality through the support and participation of customers, partners, 
            and communities across Papua New Guinea. Together, we can create a more connected and prosperous future.
        </p>
        <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
            <a href="<?php echo SITE_URL; ?>/shop/products.php" class="btn btn-primary" style="background-color: var(--medium-brown);">
                <i class="fas fa-shopping-bag"></i> Start Shopping
            </a>
            <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline" style="border-color: var(--cream); color: var(--cream);">
                <i class="fas fa-handshake"></i> Partner With Us
            </a>
        </div>
    </div>
</div>

<style>
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .col-3, .col-4, .col-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    section {
        padding: 60px 0 !important;
    }

    .row {
        margin-bottom: 40px !important;
    }

    div[style*="onmouseover"] {
        transform: none !important;
        transition: none !important;
    }

    div[style*="onmouseover"]:hover {
        transform: none !important;
    }
}

@media (max-width: 480px) {
    section {
        padding: 40px 0 !important;
        margin-bottom: 40px !important;
    }

    div[style*="padding: 40px"] {
        padding: 25px !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
