<?php
require_once '../config/config.php';

$pageTitle = 'About Us';
$pageDescription = 'Learn about Geu\'s Galore - Papua New Guinea\'s premier online store committed to quality products and exceptional service.';

include '../includes/header.php';
?>

<!-- Epic Page Header -->
<section style="padding: 120px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); position: relative; overflow: hidden;">
    <div class="container" style="position: relative; z-index: 10;">
        <div style="text-align: center;">
            <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                <i class="fas fa-info-circle"></i>
                About Our Company
            </div>
            <h1 style="font-size: clamp(2.5rem, 6vw, 4.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                About <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"><?php echo SITE_NAME; ?></span>
            </h1>
            <p style="font-size: clamp(1.2rem, 3vw, 1.6rem); color: rgba(255,255,255,0.9); max-width: 700px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                Papua New Guinea's premier online store, dedicated to bringing you quality products with exceptional service
            </p>
        </div>
    </div>
</section>

<div class="container" style="margin: -30px auto 60px; padding: 0 20px; position: relative; z-index: 10;">

    <!-- Our Story Section -->
    <div class="row" style="margin-bottom: 80px;">
        <div class="col col-6">
            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 15px 35px rgba(0,0,0,0.1); height: 100%;">
                <h2 style="color: var(--charcoal); margin-bottom: 25px; font-size: 32px; font-weight: 800;">Our Story</h2>
                <p style="color: var(--charcoal); margin-bottom: 20px; line-height: 1.8; font-size: 16px; font-weight: 500;">
                    Founded with a vision to revolutionize online shopping in Papua New Guinea, <?php echo SITE_NAME; ?>
                    has grown from a small startup to become the country's most trusted e-commerce platform. We understand
                    the unique needs of Papua New Guinean consumers and are committed to providing them with access to
                    quality products at competitive prices.
                </p>
                <p style="color: var(--charcoal); margin-bottom: 20px; line-height: 1.8; font-size: 16px; font-weight: 500;">
                    Our journey began when we recognized the gap in the local market for reliable online shopping.
                    We set out to create a platform that not only offers a wide range of products but also ensures
                    a seamless shopping experience from browsing to delivery.
                </p>
                <p style="color: var(--charcoal); line-height: 1.8; font-size: 16px; font-weight: 500;">
                    Today, we serve customers across Papua New Guinea, offering everything from electronics and clothing
                    to home goods and outdoor equipment. Our commitment to quality, customer service, and community
                    development remains at the heart of everything we do.
                </p>
            </div>
        </div>
        <div class="col col-6">
            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%); height: 450px; border-radius: 30px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.2);">
                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb);"></div>
                <i class="fas fa-store" style="font-size: 100px; color: white; text-shadow: 0 0 30px rgba(255,255,255,0.5); z-index: 2; position: relative;"></i>
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
            </div>
        </div>
    </div>

    <!-- Values Section -->
    <section style="padding: 80px 0; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ffd93d 100%); position: relative; overflow: hidden; border-radius: 30px; margin-bottom: 80px;">
        <div style="position: relative; z-index: 10; padding: 0 40px;">
            <div style="text-align: center; margin-bottom: 60px;">
                <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                    <i class="fas fa-heart"></i>
                    Our Core Values
                </div>
                <h2 style="font-size: clamp(2.5rem, 5vw, 3.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                    What <span style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Drives Us</span>
                </h2>
            </div>
            <div class="row">
                <div class="col col-3" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-heart" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Customer First</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Every decision we make is centered around providing the best possible experience for our customers.
                        </p>
                    </div>
                </div>
                <div class="col col-3" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-gem" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Quality Assurance</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            We carefully curate our product selection to ensure only the highest quality items reach our customers.
                        </p>
                    </div>
                </div>
                <div class="col col-3" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-handshake" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Trust & Integrity</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            We build lasting relationships through honest business practices and transparent communication.
                        </p>
                    </div>
                </div>
                <div class="col col-3" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 90px; height: 90px; background: rgba(255,255,255,0.15); border-radius: 25px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <i class="fas fa-leaf" style="font-size: 35px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Sustainability</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            We're committed to environmentally responsible practices and supporting local communities.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section style="padding: 80px 0; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%); position: relative; overflow: hidden; border-radius: 30px; margin-bottom: 80px;">
        <div style="position: relative; z-index: 10; padding: 0 40px;">
            <div style="text-align: center; margin-bottom: 60px;">
                <div style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); color: white; padding: 15px 30px; border-radius: 50px; margin-bottom: 30px; font-size: 16px; font-weight: 700; border: 2px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                    <i class="fas fa-users"></i>
                    Meet Our Team
                </div>
                <h2 style="font-size: clamp(2.5rem, 5vw, 3.5rem); color: white; font-weight: 900; margin-bottom: 25px; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                    The <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">People</span> Behind Our Success
                </h2>
                <p style="font-size: clamp(1.2rem, 2.5vw, 1.4rem); color: rgba(255,255,255,0.9); max-width: 700px; margin: 0 auto; line-height: 1.6; font-weight: 500;">
                    Behind <?php echo SITE_NAME; ?> is a dedicated team of professionals who are passionate about
                    e-commerce, technology, and serving the Papua New Guinea community.
                </p>
            </div>
            <div class="row">
                <div class="col col-4" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffd93d, #ff8e53, #ffd93d); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #ff6b6b, #ff8e53); border-radius: 50%; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.2);">
                            <i class="fas fa-user" style="font-size: 50px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Leadership Team</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Experienced leaders with deep understanding of the Papua New Guinea market and e-commerce industry.
                        </p>
                    </div>
                </div>
                <div class="col col-4" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffd93d, #ff8e53, #ffd93d); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #4facfe, #00f2fe); border-radius: 50%; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.2);">
                            <i class="fas fa-cogs" style="font-size: 50px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Technical Team</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Skilled developers and engineers ensuring our platform runs smoothly and securely 24/7.
                        </p>
                    </div>
                </div>
                <div class="col col-4" style="margin-bottom: 40px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 30px; padding: 40px 30px; text-align: center; border: 2px solid rgba(255,255,255,0.2); transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275); height: 100%; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.1);"
                         onmouseover="this.style.transform='translateY(-20px) scale(1.05)'; this.style.background='rgba(255,255,255,0.15)'; this.style.boxShadow='0 30px 60px rgba(0,0,0,0.2)'"
                         onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.background='rgba(255,255,255,0.1)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.1)'">
                        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffd93d, #ff8e53, #ffd93d); border-radius: 30px 30px 0 0;"></div>
                        <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #43e97b, #38f9d7); border-radius: 50%; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.2);">
                            <i class="fas fa-headset" style="font-size: 50px; color: white; z-index: 2; position: relative;"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
                        </div>
                        <h3 style="color: white; font-size: 24px; font-weight: 800; margin-bottom: 15px; text-shadow: 0 0 20px rgba(255,255,255,0.5);">Customer Service</h3>
                        <p style="color: rgba(255,255,255,0.9); line-height: 1.6; font-size: 16px; font-weight: 500;">
                            Friendly and knowledgeable support staff ready to assist you with any questions or concerns.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact CTA -->
    <section style="padding: 80px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); position: relative; overflow: hidden; border-radius: 30px;">
        <div style="position: relative; z-index: 10; text-align: center; padding: 0 40px;">
            <div style="width: 100px; height: 100px; background: rgba(255,255,255,0.15); backdrop-filter: blur(20px); border-radius: 30px; margin: 0 auto 30px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(255,255,255,0.2); position: relative; overflow: hidden;">
                <i class="fas fa-envelope" style="font-size: 40px; color: white; z-index: 2; position: relative;"></i>
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
            </div>
            <h2 style="margin-bottom: 20px; font-size: clamp(2.5rem, 5vw, 3.5rem); font-weight: 900; color: white; text-shadow: 0 0 40px rgba(255,255,255,0.5);">
                Get in <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Touch</span>
            </h2>
            <p style="margin-bottom: 40px; font-size: clamp(1.2rem, 2.5vw, 1.4rem); color: rgba(255,255,255,0.9); line-height: 1.6; font-weight: 500; max-width: 600px; margin-left: auto; margin-right: auto;">
                Have questions about our products or services? We'd love to hear from you!
            </p>
            <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                <a href="<?php echo SITE_URL; ?>/contact.php"
                   style="display: inline-flex; align-items: center; gap: 12px; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%); color: white; padding: 20px 40px; border-radius: 50px; text-decoration: none; font-weight: 700; font-size: 18px; transition: all 0.4s ease; box-shadow: 0 15px 35px rgba(255,107,107,0.4); border: 2px solid transparent;"
                   onmouseover="this.style.transform='translateY(-5px) scale(1.05)'; this.style.boxShadow='0 25px 50px rgba(255,107,107,0.6)'"
                   onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 15px 35px rgba(255,107,107,0.4)'">
                    <i class="fas fa-envelope"></i> Contact Us
                </a>
                <a href="<?php echo SITE_URL; ?>/quote/request.php"
                   style="display: inline-flex; align-items: center; gap: 12px; background: rgba(255,255,255,0.1); color: white; border: 2px solid rgba(255,255,255,0.3); padding: 20px 40px; border-radius: 50px; text-decoration: none; font-weight: 700; font-size: 18px; transition: all 0.4s ease; backdrop-filter: blur(10px);"
                   onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.borderColor='rgba(255,255,255,0.6)'; this.style.transform='translateY(-5px) scale(1.05)'"
                   onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.borderColor='rgba(255,255,255,0.3)'; this.style.transform='translateY(0) scale(1)'">
                    <i class="fas fa-file-invoice"></i> Get a Quote
                </a>
            </div>
        </div>
    </section>
</div>

<style>
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .col-3, .col-4, .col-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    section {
        padding: 60px 0 !important;
    }

    .row {
        margin-bottom: 40px !important;
    }
}

@media (max-width: 480px) {
    section {
        padding: 40px 0 !important;
        margin-bottom: 40px !important;
    }

    div[style*="padding: 40px"] {
        padding: 25px !important;
    }
}

/* Additional responsive styles for better mobile experience */
@media (max-width: 768px) {
    div[style*="onmouseover"] {
        transform: none !important;
        transition: none !important;
    }

    div[style*="onmouseover"]:hover {
        transform: none !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
