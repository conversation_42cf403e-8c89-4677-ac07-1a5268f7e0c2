<?php
require_once '../config/config.php';

$pageTitle = 'Admin Dashboard';

// Check if user is admin
if (!is_admin()) {
    redirect('login.php');
}

$db = GeusGalore\Database::getInstance();

// Get comprehensive dashboard stats
$stats = [
    'total_products' => $db->fetch("SELECT COUNT(*) as count FROM products")['count'],
    'active_products' => $db->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'],
    'low_stock_products' => $db->fetch("SELECT COUNT(*) as count FROM products WHERE stock_quantity < 10")['count'],

    'total_orders' => $db->fetch("SELECT COUNT(*) as count FROM orders")['count'],
    'pending_orders' => $db->fetch("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count'],
    'today_orders' => $db->fetch("SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()")['count'],

    'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'active_users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'],
    'new_users_today' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()")['count'],

    'total_quotes' => $db->fetch("SELECT COUNT(*) as count FROM quote_requests")['count'],
    'pending_quotes' => $db->fetch("SELECT COUNT(*) as count FROM quote_requests WHERE status = 'pending'")['count'],

    'total_revenue' => $db->fetch("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE status IN ('delivered', 'completed')")['total'],
    'monthly_revenue' => $db->fetch("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE status IN ('delivered', 'completed') AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())")['total'],

    'total_categories' => $db->fetch("SELECT COUNT(*) as count FROM categories")['count']
];

// Get recent activities
$recent_orders = $db->fetchAll("
    SELECT o.*, u.first_name, u.last_name
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    ORDER BY o.created_at DESC
    LIMIT 5
");

$recent_users = $db->fetchAll("
    SELECT * FROM users
    ORDER BY created_at DESC
    LIMIT 5
");

$recent_quotes = $db->fetchAll("
    SELECT q.*, p.name as product_name
    FROM quote_requests q
    LEFT JOIN products p ON q.product_id = p.id
    ORDER BY q.created_at DESC
    LIMIT 5
");

$low_stock_products = $db->fetchAll("
    SELECT * FROM products
    WHERE stock_quantity < 10 AND status = 'active'
    ORDER BY stock_quantity ASC
    LIMIT 5
");

include 'includes/admin-header.php';
?>

<div class="container" style="margin: 40px auto; padding: 0 20px;">
    <!-- Welcome Header -->
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 20px; margin-bottom: 40px; text-align: center;">
        <h1 style="margin: 0 0 15px 0; font-size: 36px; font-weight: 900;">
            <i class="fas fa-tachometer-alt"></i> Welcome to Your Dashboard
        </h1>
        <p style="margin: 0; font-size: 18px; opacity: 0.9;">
            Manage your <?php echo SITE_NAME; ?> e-commerce platform
        </p>
        <div style="margin-top: 20px; font-size: 14px; opacity: 0.8;">
            Last login: <?php echo date('M j, Y g:i A'); ?>
        </div>
    </div>

    <!-- Main Stats Cards -->
    <div class="row" style="margin-bottom: 40px;">
        <div class="col col-3">
            <div class="modern-card" style="background: var(--gradient-ocean); color: white; text-align: center; padding: 25px; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);"></div>
                <div style="font-size: 36px; font-weight: 900; margin-bottom: 10px; text-shadow: 0 0 20px rgba(255,255,255,0.5);" class="stat-number">
                    <?php echo number_format($stats['total_products']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 10px;">
                    <i class="fas fa-box"></i> Total Products
                </div>
                <div style="font-size: 12px; opacity: 0.8;">
                    <?php echo number_format($stats['active_products']); ?> active
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="background: var(--gradient-sunset); color: white; text-align: center; padding: 25px; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);"></div>
                <div style="font-size: 36px; font-weight: 900; margin-bottom: 10px; text-shadow: 0 0 20px rgba(255,255,255,0.5);" class="stat-number">
                    <?php echo number_format($stats['total_orders']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 10px;">
                    <i class="fas fa-shopping-cart"></i> Total Orders
                </div>
                <div style="font-size: 12px; opacity: 0.8;">
                    <?php echo number_format($stats['today_orders']); ?> today
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="background: var(--gradient-warm); color: white; text-align: center; padding: 25px; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);"></div>
                <div style="font-size: 36px; font-weight: 900; margin-bottom: 10px; text-shadow: 0 0 20px rgba(255,255,255,0.5);" class="stat-number">
                    <?php echo number_format($stats['total_users']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 10px;">
                    <i class="fas fa-users"></i> Total Users
                </div>
                <div style="font-size: 12px; opacity: 0.8;">
                    <?php echo number_format($stats['new_users_today']); ?> new today
                </div>
            </div>
        </div>
        <div class="col col-3">
            <div class="modern-card" style="background: var(--gradient-forest); color: white; text-align: center; padding: 25px; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);"></div>
                <div style="font-size: 28px; font-weight: 900; margin-bottom: 10px; text-shadow: 0 0 20px rgba(255,255,255,0.5);" class="stat-number">
                    <?php echo format_currency($stats['total_revenue']); ?>
                </div>
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 10px;">
                    <i class="fas fa-dollar-sign"></i> Total Revenue
                </div>
                <div style="font-size: 12px; opacity: 0.8;">
                    <?php echo format_currency($stats['monthly_revenue']); ?> this month
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Stats -->
    <div class="row" style="margin-bottom: 40px;">
        <div class="col col-4">
            <div class="modern-card" style="background: var(--gradient-purple); color: white; text-align: center; padding: 20px;">
                <div style="font-size: 28px; font-weight: 900; margin-bottom: 8px;" class="stat-number">
                    <?php echo number_format($stats['total_quotes']); ?>
                </div>
                <div style="font-size: 14px; font-weight: 600;">
                    <i class="fas fa-file-invoice"></i> Quote Requests
                </div>
                <div style="font-size: 11px; opacity: 0.8;">
                    <?php echo number_format($stats['pending_quotes']); ?> pending
                </div>
            </div>
        </div>
        <div class="col col-4">
            <div class="modern-card" style="background: var(--gradient-coral); color: white; text-align: center; padding: 20px;">
                <div style="font-size: 28px; font-weight: 900; margin-bottom: 8px;" class="stat-number">
                    <?php echo number_format($stats['pending_orders']); ?>
                </div>
                <div style="font-size: 14px; font-weight: 600;">
                    <i class="fas fa-clock"></i> Pending Orders
                </div>
                <div style="font-size: 11px; opacity: 0.8;">
                    Need attention
                </div>
            </div>
        </div>
        <div class="col col-4">
            <div class="modern-card" style="background: var(--gradient-warning); color: white; text-align: center; padding: 20px;">
                <div style="font-size: 28px; font-weight: 900; margin-bottom: 8px;" class="stat-number">
                    <?php echo number_format($stats['low_stock_products']); ?>
                </div>
                <div style="font-size: 14px; font-weight: 600;">
                    <i class="fas fa-exclamation-triangle"></i> Low Stock
                </div>
                <div style="font-size: 11px; opacity: 0.8;">
                    Products < 10 units
                </div>
            </div>
        </div>
    </div>
    <!-- Recent Activities Section -->
    <div class="row" style="margin-bottom: 40px;">
        <!-- Recent Orders -->
        <div class="col col-6">
            <div class="data-table">
                <div style="padding: 20px; background: var(--gradient-ocean); color: white; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; font-weight: 700;">
                        <i class="fas fa-shopping-cart"></i> Recent Orders
                    </h3>
                    <a href="orders.php" style="color: white; text-decoration: none; font-size: 12px; opacity: 0.9;">
                        View All <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <?php if (empty($recent_orders)): ?>
                    <div style="text-align: center; padding: 40px 20px; color: var(--medium-brown);">
                        <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>No orders yet</p>
                    </div>
                <?php else: ?>
                    <div style="padding: 20px;">
                        <?php foreach ($recent_orders as $order): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--cream);">
                                <div>
                                    <div style="font-weight: 600; color: var(--coral); margin-bottom: 3px;">
                                        #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?>
                                    </div>
                                    <div style="font-size: 12px; color: var(--medium-brown);">
                                        <?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 600; color: var(--success);">
                                        <?php echo format_currency($order['total_amount']); ?>
                                    </div>
                                    <div style="font-size: 11px; color: var(--medium-brown);">
                                        <?php echo date('M j', strtotime($order['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="col col-6">
            <div class="data-table">
                <div style="padding: 20px; background: var(--gradient-forest); color: white; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; font-weight: 700;">
                        <i class="fas fa-users"></i> Recent Users
                    </h3>
                    <a href="users.php" style="color: white; text-decoration: none; font-size: 12px; opacity: 0.9;">
                        View All <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <?php if (empty($recent_users)): ?>
                    <div style="text-align: center; padding: 40px 20px; color: var(--medium-brown);">
                        <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>No users yet</p>
                    </div>
                <?php else: ?>
                    <div style="padding: 20px;">
                        <?php foreach ($recent_users as $user): ?>
                            <div style="display: flex; align-items: center; gap: 15px; padding: 12px 0; border-bottom: 1px solid var(--cream);">
                                <div style="width: 40px; height: 40px; background: var(--gradient-sunset); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px;">
                                    <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--charcoal); font-size: 14px;">
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                    </div>
                                    <div style="color: var(--medium-brown); font-size: 12px;">
                                        <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Additional Sections -->
    <div class="row" style="margin-bottom: 40px;">
        <!-- Recent Quotes -->
        <div class="col col-6">
            <div class="data-table">
                <div style="padding: 20px; background: var(--gradient-purple); color: white; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; font-weight: 700;">
                        <i class="fas fa-file-invoice"></i> Recent Quotes
                    </h3>
                    <a href="quotes.php" style="color: white; text-decoration: none; font-size: 12px; opacity: 0.9;">
                        View All <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <?php if (empty($recent_quotes)): ?>
                    <div style="text-align: center; padding: 40px 20px; color: var(--medium-brown);">
                        <i class="fas fa-file-invoice" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>No quotes yet</p>
                    </div>
                <?php else: ?>
                    <div style="padding: 20px;">
                        <?php foreach ($recent_quotes as $quote): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--cream);">
                                <div>
                                    <div style="font-weight: 600; color: var(--coral); margin-bottom: 3px;">
                                        #Q<?php echo str_pad($quote['id'], 6, '0', STR_PAD_LEFT); ?>
                                    </div>
                                    <div style="font-size: 12px; color: var(--medium-brown);">
                                        <?php echo htmlspecialchars($quote['contact_name']); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 12px; color: var(--medium-brown);">
                                        <?php echo $quote['product_name'] ? htmlspecialchars($quote['product_name']) : 'Custom'; ?>
                                    </div>
                                    <div style="font-size: 11px; color: var(--medium-brown);">
                                        <?php echo date('M j', strtotime($quote['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Low Stock Products -->
        <div class="col col-6">
            <div class="data-table">
                <div style="padding: 20px; background: var(--gradient-warning); color: white; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; font-weight: 700;">
                        <i class="fas fa-exclamation-triangle"></i> Low Stock Alert
                    </h3>
                    <a href="products.php?filter=low_stock" style="color: white; text-decoration: none; font-size: 12px; opacity: 0.9;">
                        View All <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <?php if (empty($low_stock_products)): ?>
                    <div style="text-align: center; padding: 40px 20px; color: var(--medium-brown);">
                        <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5; color: var(--success);"></i>
                        <p>All products well stocked!</p>
                    </div>
                <?php else: ?>
                    <div style="padding: 20px;">
                        <?php foreach ($low_stock_products as $product): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--cream);">
                                <div>
                                    <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 3px;">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </div>
                                    <div style="font-size: 12px; color: var(--medium-brown);">
                                        ID: <?php echo $product['id']; ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 600; color: var(--danger); font-size: 14px;">
                                        <?php echo number_format($product['stock_quantity']); ?> left
                                    </div>
                                    <div style="font-size: 11px; color: var(--medium-brown);">
                                        <?php echo format_currency($product['price']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="modern-card" style="padding: 30px; text-align: center; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
        <h3 style="margin-bottom: 25px; color: var(--charcoal); font-weight: 700;">
            <i class="fas fa-bolt"></i> Quick Actions
        </h3>
        <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
            <a href="product-add.php" class="btn btn-primary" style="background: var(--gradient-ocean); border: none; padding: 15px 25px;">
                <i class="fas fa-plus"></i> Add Product
            </a>
            <a href="categories.php" class="btn btn-primary" style="background: var(--gradient-sunset); border: none; padding: 15px 25px;">
                <i class="fas fa-tags"></i> Manage Categories
            </a>
            <a href="orders.php?status=pending" class="btn btn-primary" style="background: var(--gradient-warm); border: none; padding: 15px 25px;">
                <i class="fas fa-clock"></i> Pending Orders
            </a>
            <a href="quotes.php?status=pending" class="btn btn-primary" style="background: var(--gradient-purple); border: none; padding: 15px 25px;">
                <i class="fas fa-file-invoice"></i> Pending Quotes
            </a>
        </div>
    </div>
</div>

<script>
// Dashboard interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Animate stat cards on load
    const statCards = document.querySelectorAll('.stat-number');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });

    // Auto-refresh stats every 60 seconds
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const newDoc = parser.parseFromString(html, 'text/html');

                    // Update stat numbers
                    const statNumbers = document.querySelectorAll('.stat-number');
                    const newStatNumbers = newDoc.querySelectorAll('.stat-number');

                    statNumbers.forEach((stat, index) => {
                        if (newStatNumbers[index]) {
                            stat.textContent = newStatNumbers[index].textContent;
                        }
                    });
                })
                .catch(error => console.log('Auto-refresh failed:', error));
        }
    }, 60000); // 60 seconds
});
</script>

<?php include 'includes/admin-footer.php'; ?>
