<?php
require_once '../config/config.php';

$pageTitle = 'Admin Dashboard';

// Check if user is admin
if (!is_admin()) {
    redirect(SITE_URL . '/admin/login.php');
}

$db = GeusGalore\Database::getInstance();

// Get dashboard stats
$stats = [
    'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'total_products' => $db->fetch("SELECT COUNT(*) as count FROM products")['count'],
    'total_orders' => $db->fetch("SELECT COUNT(*) as count FROM orders")['count'],
    'total_revenue' => $db->fetch("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE status = 'completed'")['total'],
    'pending_orders' => $db->fetch("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count'],
    'low_stock' => $db->fetch("SELECT COUNT(*) as count FROM products WHERE stock_quantity < 10")['count']
];

// Get recent orders
$recentOrders = $db->fetchAll("
    SELECT o.*, u.first_name, u.last_name, u.email 
    FROM orders o 
    LEFT JOIN users u ON o.user_id = u.id 
    ORDER BY o.created_at DESC 
    LIMIT 10
");

// Get recent users
$recentUsers = $db->fetchAll("
    SELECT * FROM users 
    ORDER BY created_at DESC 
    LIMIT 5
");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, var(--light-peach) 0%, var(--cream) 100%);
        }
        .admin-header {
            background: var(--gradient-sunset);
            color: white;
            padding: 20px 0;
            box-shadow: var(--shadow-lg);
        }
        .admin-nav {
            background: linear-gradient(135deg, var(--charcoal) 0%, var(--dark-brown) 100%);
            padding: 15px 0;
            box-shadow: var(--shadow-md);
        }
        .admin-nav a {
            color: white;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 500;
        }
        .admin-nav a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(145deg, var(--white) 0%, var(--cream) 100%);
            border-radius: var(--radius-lg);
            padding: 25px;
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        .stat-card.users::before { background: var(--gradient-sunset); }
        .stat-card.products::before { background: var(--gradient-ocean); }
        .stat-card.orders::before { background: var(--gradient-forest); }
        .stat-card.revenue::before { background: var(--gradient-warm); }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }
        .stat-number {
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: var(--medium-brown);
        }
        .data-table {
            background: linear-gradient(145deg, var(--white) 0%, var(--cream) 100%);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .data-table table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th {
            background: var(--gradient-sunset);
            color: white;
            padding: 15px;
            font-weight: 600;
            text-align: left;
        }
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255,255,255,0.3);
        }
        .data-table tr:hover {
            background: rgba(255, 107, 107, 0.05);
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h1 style="margin: 0; font-size: 28px; font-weight: 800;">
                    <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                </h1>
                <div style="display: flex; align-items: center; gap: 20px;">
                    <span>Welcome, <?php echo $_SESSION['admin_username']; ?>!</span>
                    <a href="<?php echo SITE_URL; ?>" style="color: white; text-decoration: none; padding: 8px 16px; background: rgba(255,255,255,0.2); border-radius: 20px;">
                        <i class="fas fa-external-link-alt"></i> View Site
                    </a>
                    <a href="<?php echo SITE_URL; ?>/admin/logout.php" style="color: white; text-decoration: none; padding: 8px 16px; background: rgba(255,255,255,0.2); border-radius: 20px;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Navigation -->
    <div class="admin-nav">
        <div class="container">
            <div style="display: flex; flex-wrap: wrap; align-items: center;">
                <a href="<?php echo SITE_URL; ?>/admin/index.php">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/products.php">
                    <i class="fas fa-box"></i> Products
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/categories.php">
                    <i class="fas fa-tags"></i> Categories
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/orders.php">
                    <i class="fas fa-shopping-cart"></i> Orders
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/users.php">
                    <i class="fas fa-users"></i> Users
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/quotes.php">
                    <i class="fas fa-file-invoice"></i> Quotes
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/settings.php">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container" style="margin: 40px auto; padding: 0 20px;">
        <!-- Stats Cards -->
        <div class="row" style="margin-bottom: 40px;">
            <div class="col col-3">
                <div class="stat-card users">
                    <div class="stat-number" style="background: var(--gradient-sunset); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?php echo number_format($stats['total_users']); ?>
                    </div>
                    <div class="stat-label">
                        <i class="fas fa-users"></i> Total Users
                    </div>
                </div>
            </div>
            <div class="col col-3">
                <div class="stat-card products">
                    <div class="stat-number" style="background: var(--gradient-ocean); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?php echo number_format($stats['total_products']); ?>
                    </div>
                    <div class="stat-label">
                        <i class="fas fa-box"></i> Total Products
                    </div>
                </div>
            </div>
            <div class="col col-3">
                <div class="stat-card orders">
                    <div class="stat-number" style="background: var(--gradient-forest); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?php echo number_format($stats['total_orders']); ?>
                    </div>
                    <div class="stat-label">
                        <i class="fas fa-shopping-cart"></i> Total Orders
                    </div>
                </div>
            </div>
            <div class="col col-3">
                <div class="stat-card revenue">
                    <div class="stat-number" style="background: var(--gradient-warm); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?php echo format_currency($stats['total_revenue']); ?>
                    </div>
                    <div class="stat-label">
                        <i class="fas fa-dollar-sign"></i> Total Revenue
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Cards -->
        <div class="row" style="margin-bottom: 40px;">
            <div class="col col-6">
                <div class="modern-card" style="background: var(--gradient-warm); color: white;">
                    <h3 style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-exclamation-triangle"></i> Pending Orders
                    </h3>
                    <div style="font-size: 24px; font-weight: 800; margin-bottom: 10px;">
                        <?php echo $stats['pending_orders']; ?>
                    </div>
                    <p>Orders waiting for processing</p>
                    <a href="<?php echo SITE_URL; ?>/admin/orders.php?status=pending" 
                       style="color: white; text-decoration: none; font-weight: 600; padding: 8px 16px; background: rgba(255,255,255,0.2); border-radius: 20px; display: inline-block; margin-top: 10px;">
                        View Orders <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
            <div class="col col-6">
                <div class="modern-card" style="background: var(--gradient-sunset); color: white;">
                    <h3 style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-box-open"></i> Low Stock Alert
                    </h3>
                    <div style="font-size: 24px; font-weight: 800; margin-bottom: 10px;">
                        <?php echo $stats['low_stock']; ?>
                    </div>
                    <p>Products with less than 10 items</p>
                    <a href="<?php echo SITE_URL; ?>/admin/products.php?filter=low_stock" 
                       style="color: white; text-decoration: none; font-weight: 600; padding: 8px 16px; background: rgba(255,255,255,0.2); border-radius: 20px; display: inline-block; margin-top: 10px;">
                        View Products <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Orders -->
            <div class="col col-8">
                <div class="data-table">
                    <h3 style="padding: 20px; margin: 0; background: var(--gradient-ocean); color: white; font-weight: 700;">
                        <i class="fas fa-shopping-cart"></i> Recent Orders
                    </h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recentOrders)): ?>
                                <tr>
                                    <td colspan="6" style="text-align: center; padding: 40px; color: var(--medium-brown);">
                                        <i class="fas fa-shopping-cart" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                                        <br>No orders yet
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td style="font-weight: 600; color: var(--coral);">
                                            #<?php echo $order['order_number']; ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                                        </td>
                                        <td style="font-weight: 600;">
                                            <?php echo format_currency($order['total_amount']); ?>
                                        </td>
                                        <td>
                                            <span style="padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; text-transform: uppercase;
                                                <?php
                                                switch($order['status']) {
                                                    case 'completed': echo 'background: var(--success); color: white;'; break;
                                                    case 'processing': echo 'background: var(--warning); color: white;'; break;
                                                    case 'pending': echo 'background: var(--info); color: white;'; break;
                                                    default: echo 'background: var(--medium-brown); color: white;';
                                                }
                                                ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?php echo SITE_URL; ?>/admin/order-details.php?id=<?php echo $order['id']; ?>" 
                                               style="color: var(--coral); text-decoration: none; font-weight: 600;">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Users -->
            <div class="col col-4">
                <div class="data-table">
                    <h3 style="padding: 20px; margin: 0; background: var(--gradient-forest); color: white; font-weight: 700;">
                        <i class="fas fa-users"></i> Recent Users
                    </h3>
                    <div style="padding: 20px;">
                        <?php if (empty($recentUsers)): ?>
                            <div style="text-align: center; color: var(--medium-brown); padding: 20px;">
                                <i class="fas fa-users" style="font-size: 32px; margin-bottom: 10px; opacity: 0.5;"></i>
                                <br>No users yet
                            </div>
                        <?php else: ?>
                            <?php foreach ($recentUsers as $user): ?>
                                <div style="display: flex; align-items: center; gap: 15px; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.3);">
                                    <div style="width: 40px; height: 40px; background: var(--gradient-sunset); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px;">
                                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                    </div>
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: var(--charcoal); font-size: 14px;">
                                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                        </div>
                                        <div style="color: var(--medium-brown); font-size: 12px;">
                                            <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stat cards on load
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
