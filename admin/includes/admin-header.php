<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* Admin-specific styles */
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(102,126,234,0.2);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .admin-nav {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(79,172,254,0.2);
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            margin-right: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .admin-nav a:hover, .admin-nav a.active {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            text-decoration: none;
            color: white;
            background: var(--coral);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-icon:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .btn-icon.btn-danger {
            background: var(--danger);
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: var(--success);
            color: white;
        }
        
        .status-inactive {
            background: var(--danger);
            color: white;
        }
        
        .status-draft {
            background: var(--warning);
            color: var(--dark-brown);
        }
        
        .status-pending {
            background: var(--warning);
            color: var(--dark-brown);
        }
        
        .status-completed {
            background: var(--success);
            color: white;
        }
        
        .status-cancelled {
            background: var(--danger);
            color: white;
        }
        
        .data-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .data-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: var(--cream);
            color: var(--dark-brown);
            padding: 15px;
            text-align: left;
            font-weight: 700;
            border-bottom: 2px solid var(--light-brown);
        }
        
        .data-table td {
            padding: 15px;
            border-bottom: 1px solid var(--cream);
            vertical-align: middle;
        }
        
        .data-table tr:hover {
            background: var(--cream);
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .admin-nav a {
                margin-bottom: 10px;
                margin-right: 10px;
            }
            
            .data-table {
                overflow-x: auto;
            }
            
            .data-table table {
                min-width: 800px;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                <div>
                    <h1 style="margin: 0; font-size: 28px; font-weight: 800;">
                        <i class="fas fa-tachometer-alt"></i> <?php echo SITE_NAME; ?> Admin
                    </h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">
                        Welcome back, <?php echo $_SESSION['admin_username']; ?>!
                    </p>
                </div>
                <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                    <a href="<?php echo SITE_URL; ?>" target="_blank" 
                       style="color: white; text-decoration: none; padding: 10px 20px; background: rgba(255,255,255,0.2); border-radius: 25px; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-external-link-alt"></i> View Site
                    </a>
                    <a href="logout.php" 
                       style="color: white; text-decoration: none; padding: 10px 20px; background: rgba(255,107,107,0.8); border-radius: 25px; font-weight: 600; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,107,107,1)'"
                       onmouseout="this.style.background='rgba(255,107,107,0.8)'">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Navigation -->
    <div class="admin-nav">
        <div class="container">
            <div style="display: flex; flex-wrap: wrap; align-items: center;">
                <a href="index.php" <?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="products.php" <?php echo basename($_SERVER['PHP_SELF']) === 'products.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-box"></i> Products
                </a>
                <a href="categories.php" <?php echo basename($_SERVER['PHP_SELF']) === 'categories.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-tags"></i> Categories
                </a>
                <a href="orders.php" <?php echo basename($_SERVER['PHP_SELF']) === 'orders.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-shopping-cart"></i> Orders
                </a>
                <a href="users.php" <?php echo basename($_SERVER['PHP_SELF']) === 'users.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-users"></i> Users
                </a>
                <a href="quotes.php" <?php echo basename($_SERVER['PHP_SELF']) === 'quotes.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-file-invoice"></i> Quotes
                </a>
                <a href="analytics.php" <?php echo basename($_SERVER['PHP_SELF']) === 'analytics.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-chart-bar"></i> Analytics
                </a>
                <a href="settings.php" <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'class="active"' : ''; ?>>
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
        </div>
    </div>
