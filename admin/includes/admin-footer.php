    <!-- Admin <PERSON>er -->
    <footer style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 40px 0 20px; margin-top: 60px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-bottom: 30px;">
                <div>
                    <h4 style="font-size: 20px; font-weight: 700; margin-bottom: 15px; color: #ffd93d;">
                        <i class="fas fa-tachometer-alt"></i> Admin Panel
                    </h4>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">
                        Manage your <?php echo SITE_NAME; ?> e-commerce platform with powerful admin tools.
                    </p>
                </div>
                
                <div>
                    <h4 style="font-size: 18px; font-weight: 700; margin-bottom: 15px; color: #ffd93d;">Quick Links</h4>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <a href="index.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                        <a href="products.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">
                            <i class="fas fa-box"></i> Products
                        </a>
                        <a href="orders.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">
                            <i class="fas fa-shopping-cart"></i> Orders
                        </a>
                        <a href="users.php" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 style="font-size: 18px; font-weight: 700; margin-bottom: 15px; color: #ffd93d;">System Info</h4>
                    <div style="color: rgba(255,255,255,0.8); font-size: 14px; line-height: 1.6;">
                        <div><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></div>
                        <div><strong>Server Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></div>
                        <div><strong>Admin:</strong> <?php echo $_SESSION['admin_username']; ?></div>
                        <div><strong>Role:</strong> <?php echo ucfirst($_SESSION['admin_role']); ?></div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.2);">
                <p style="color: rgba(255,255,255,0.6); margin: 0;">
                    &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?> Admin Panel. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Admin JavaScript -->
    <script>
        // Auto-hide flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });

        // Confirm delete actions
        function confirmDelete(message = 'Are you sure you want to delete this item?') {
            return confirm(message);
        }

        // Auto-refresh dashboard stats every 30 seconds
        if (window.location.pathname.includes('admin/index.php')) {
            setInterval(function() {
                // Only refresh if user is still on the page
                if (document.visibilityState === 'visible') {
                    fetch(window.location.href)
                        .then(response => response.text())
                        .then(html => {
                            const parser = new DOMParser();
                            const newDoc = parser.parseFromString(html, 'text/html');
                            
                            // Update stat numbers
                            const statNumbers = document.querySelectorAll('.stat-number');
                            const newStatNumbers = newDoc.querySelectorAll('.stat-number');
                            
                            statNumbers.forEach((stat, index) => {
                                if (newStatNumbers[index]) {
                                    stat.textContent = newStatNumbers[index].textContent;
                                }
                            });
                        })
                        .catch(error => console.log('Auto-refresh failed:', error));
                }
            }, 30000); // 30 seconds
        }

        // Table row highlighting
        document.addEventListener('DOMContentLoaded', function() {
            const tableRows = document.querySelectorAll('.data-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'var(--cream)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        });

        // Mobile menu toggle for admin nav
        function toggleMobileMenu() {
            const nav = document.querySelector('.admin-nav');
            nav.classList.toggle('mobile-open');
        }

        // Add mobile menu styles
        const mobileStyles = `
            @media (max-width: 768px) {
                .admin-nav .container > div {
                    flex-direction: column;
                    align-items: stretch;
                }
                
                .admin-nav a {
                    margin-right: 0;
                    margin-bottom: 5px;
                    text-align: center;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = mobileStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
