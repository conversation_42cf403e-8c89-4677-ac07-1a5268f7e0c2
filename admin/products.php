<?php
require_once '../config/config.php';

$pageTitle = 'Product Management';

// Check if user is admin
if (!is_admin()) {
    redirect('login.php');
}

$db = GeusGalore\Database::getInstance();
$product = new GeusGalore\Product();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'delete' && isset($_POST['product_id'])) {
        $productId = intval($_POST['product_id']);
        
        // Delete product images first
        $images = $db->fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
        foreach ($images as $image) {
            $imagePath = ROOT_PATH . '/uploads/products/' . $image['image_path'];
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
        
        // Delete from database
        $db->query("DELETE FROM product_images WHERE product_id = ?", [$productId]);
        $db->query("DELETE FROM products WHERE id = ?", [$productId]);
        
        $message = 'Product deleted successfully!';
        $messageType = 'success';
    }
}

// Get filters
$filters = [
    'search' => $_GET['search'] ?? '',
    'category' => $_GET['category'] ?? '',
    'status' => $_GET['status'] ?? '',
    'filter' => $_GET['filter'] ?? ''
];

// Build query
$whereConditions = [];
$params = [];

if (!empty($filters['search'])) {
    $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $params[] = '%' . $filters['search'] . '%';
    $params[] = '%' . $filters['search'] . '%';
}

if (!empty($filters['category'])) {
    $whereConditions[] = "p.category_id = ?";
    $params[] = $filters['category'];
}

if (!empty($filters['status'])) {
    $whereConditions[] = "p.status = ?";
    $params[] = $filters['status'];
}

if ($filters['filter'] === 'low_stock') {
    $whereConditions[] = "p.stock_quantity < 10";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get products
$page = max(1, intval($_GET['page'] ?? 1));
$limit = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

$products = $db->fetchAll("
    SELECT p.*, c.name as category_name,
           (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    $whereClause
    ORDER BY p.created_at DESC 
    LIMIT $limit OFFSET $offset
", $params);

$totalProducts = $db->fetch("SELECT COUNT(*) as count FROM products p $whereClause", $params)['count'];
$totalPages = ceil($totalProducts / $limit);

// Get categories for filter
$categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");

include 'includes/admin-header.php';
?>

<div class="container" style="margin: 40px auto; padding: 0 20px;">
    <!-- Page Header -->
    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 30px; flex-wrap: wrap; gap: 20px;">
        <div>
            <h1 style="color: var(--charcoal); margin-bottom: 10px; font-size: 32px; font-weight: 800;">
                <i class="fas fa-box"></i> Product Management
            </h1>
            <p style="color: var(--medium-brown); font-size: 16px;">Manage your product catalog</p>
        </div>
        <a href="product-add.php" class="btn btn-primary" style="background: var(--gradient-sunset); border: none; padding: 15px 30px; font-size: 16px; font-weight: 700;">
            <i class="fas fa-plus"></i> Add New Product
        </a>
    </div>

    <?php if ($message): ?>
        <div style="background: <?php echo $messageType === 'success' ? 'var(--success)' : 'var(--danger)'; ?>; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 30px;">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="modern-card" style="margin-bottom: 30px; padding: 25px;">
        <h3 style="margin-bottom: 20px; color: var(--charcoal); font-weight: 700;">
            <i class="fas fa-filter"></i> Filters
        </h3>
        
        <form method="GET" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; align-items: end;">
            <div>
                <label class="form-label">Search Products</label>
                <input type="text" name="search" class="form-control" placeholder="Search by name or description..." 
                       value="<?php echo htmlspecialchars($filters['search']); ?>">
            </div>
            
            <div>
                <label class="form-label">Category</label>
                <select name="category" class="form-control">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" 
                                <?php echo $filters['category'] == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="form-label">Status</label>
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" <?php echo $filters['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="inactive" <?php echo $filters['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    <option value="draft" <?php echo $filters['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                </select>
            </div>
            
            <div>
                <label class="form-label">Quick Filter</label>
                <select name="filter" class="form-control">
                    <option value="">No Filter</option>
                    <option value="low_stock" <?php echo $filters['filter'] === 'low_stock' ? 'selected' : ''; ?>>Low Stock</option>
                </select>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
                <a href="products.php" class="btn btn-outline">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Products Table -->
    <div class="data-table">
        <div style="padding: 20px; background: var(--gradient-ocean); color: white; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-weight: 700;">
                <i class="fas fa-list"></i> Products (<?php echo number_format($totalProducts); ?>)
            </h3>
            <div style="font-size: 14px; opacity: 0.9;">
                Page <?php echo $page; ?> of <?php echo $totalPages; ?>
            </div>
        </div>
        
        <?php if (empty($products)): ?>
            <div style="text-align: center; padding: 60px 20px; color: var(--medium-brown);">
                <i class="fas fa-box-open" style="font-size: 64px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>No products found</h3>
                <p>Try adjusting your filters or add your first product.</p>
                <a href="product-add.php" class="btn btn-primary" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Add Product
                </a>
            </div>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Product</th>
                        <th>Category</th>
                        <th>Price</th>
                        <th>Stock</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $prod): ?>
                        <tr>
                            <td>
                                <?php if ($prod['primary_image']): ?>
                                    <img src="<?php echo UPLOAD_URL . 'products/' . $prod['primary_image']; ?>" 
                                         alt="<?php echo htmlspecialchars($prod['name']); ?>"
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                                <?php else: ?>
                                    <div style="width: 50px; height: 50px; background: var(--cream); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image" style="color: var(--medium-brown);"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($prod['name']); ?>
                                </div>
                                <div style="font-size: 12px; color: var(--medium-brown);">
                                    ID: <?php echo $prod['id']; ?>
                                </div>
                            </td>
                            <td>
                                <span style="background: var(--cream); color: var(--dark-brown); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                                    <?php echo htmlspecialchars($prod['category_name'] ?: 'Uncategorized'); ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-weight: 600; color: var(--coral);">
                                    <?php echo format_currency($prod['price']); ?>
                                </div>
                                <?php if ($prod['sale_price']): ?>
                                    <div style="font-size: 12px; color: var(--medium-brown); text-decoration: line-through;">
                                        <?php echo format_currency($prod['sale_price']); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="color: <?php echo $prod['stock_quantity'] < 10 ? 'var(--danger)' : 'var(--success)'; ?>; font-weight: 600;">
                                    <?php echo number_format($prod['stock_quantity']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $prod['status']; ?>">
                                    <?php echo ucfirst($prod['status']); ?>
                                </span>
                            </td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <a href="product-edit.php?id=<?php echo $prod['id']; ?>" 
                                       class="btn-icon" title="Edit Product">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?php echo SITE_URL; ?>/shop/product-detail.php?slug=<?php echo $prod['slug']; ?>" 
                                       class="btn-icon" title="View Product" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button onclick="deleteProduct(<?php echo $prod['id']; ?>, '<?php echo htmlspecialchars($prod['name']); ?>')" 
                                            class="btn-icon btn-danger" title="Delete Product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 30px; gap: 10px;">
            <?php if ($page > 1): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                   class="btn btn-outline">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            <?php endif; ?>
            
            <span style="color: var(--medium-brown); font-weight: 600;">
                Page <?php echo $page; ?> of <?php echo $totalPages; ?>
            </span>
            
            <?php if ($page < $totalPages): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                   class="btn btn-outline">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 10000; align-items: center; justify-content: center;">
    <div style="background: white; padding: 30px; border-radius: 15px; max-width: 400px; width: 90%; text-align: center;">
        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--danger); margin-bottom: 20px;"></i>
        <h3 style="margin-bottom: 15px; color: var(--charcoal);">Delete Product</h3>
        <p style="margin-bottom: 25px; color: var(--medium-brown);">
            Are you sure you want to delete "<span id="productName"></span>"? This action cannot be undone.
        </p>
        <div style="display: flex; gap: 15px; justify-content: center;">
            <button onclick="closeDeleteModal()" class="btn btn-outline">Cancel</button>
            <form method="POST" style="display: inline;" id="deleteForm">
                <input type="hidden" name="product_id" id="deleteProductId">
                <input type="hidden" name="action" value="delete">
                <button type="submit" class="btn" style="background: var(--danger); color: white;">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
function deleteProduct(productId, productName) {
    document.getElementById('deleteProductId').value = productId;
    document.getElementById('productName').textContent = productName;
    document.getElementById('deleteModal').style.display = 'flex';
}

function closeDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// Close modal on outside click
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>

<?php include 'includes/admin-footer.php'; ?>
