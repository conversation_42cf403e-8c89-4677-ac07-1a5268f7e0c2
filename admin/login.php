<?php
require_once '../config/config.php';

$pageTitle = 'Admin Login';

// Redirect if already logged in as admin
if (is_admin()) {
    redirect('index.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        $db = GeusGalore\Database::getInstance();
        $admin = $db->fetch(
            "SELECT * FROM admin_users WHERE username = ? OR email = ?",
            [$username, $username]
        );
        
        if ($admin && password_verify($password, $admin['password'])) {
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_role'] = $admin['role'];
            
            flash_message('success', 'Welcome back, ' . $admin['username'] . '!');
            redirect('index.php');
        } else {
            $error = 'Invalid username or password.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">

    <!-- Background Pattern -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>'); opacity: 0.4;"></div>

    <!-- Epic Admin Login Card -->
    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); padding: 50px; border-radius: 30px; box-shadow: 0 25px 50px rgba(0,0,0,0.2); max-width: 450px; width: 90%; border: 2px solid rgba(255,255,255,0.2); position: relative; z-index: 10;">

        <!-- Gradient Border Effect -->
        <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #ff6b6b, #ff8e53, #ffd93d, #43e97b, #4facfe, #f093fb); border-radius: 30px 30px 0 0;"></div>

        <div style="text-align: center; margin-bottom: 40px;">
            <!-- Epic Admin Icon -->
            <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%); border-radius: 30px; margin: 0 auto 25px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden; box-shadow: 0 15px 35px rgba(255,107,107,0.4);">
                <i class="fas fa-user-shield" style="font-size: 40px; color: white; z-index: 2; position: relative;"></i>
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%); animation: shimmer 3s infinite;"></div>
            </div>

            <h1 style="color: white; margin-bottom: 15px; font-size: 32px; font-weight: 900; text-shadow: 0 0 20px rgba(255,255,255,0.5);">
                Admin <span style="background: linear-gradient(135deg, #ffd93d 0%, #ff8e53 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Login</span>
            </h1>
            <p style="color: rgba(255,255,255,0.9); font-size: 18px; font-weight: 500;"><?php echo SITE_NAME; ?> Administration</p>

            <!-- Login Instructions -->
            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 15px; margin-top: 20px; border: 1px solid rgba(255,255,255,0.2);">
                <p style="color: rgba(255,255,255,0.9); font-size: 14px; margin: 0; font-weight: 500;">
                    <i class="fas fa-info-circle" style="margin-right: 8px; color: #ffd93d;"></i>
                    Use either <strong>"admin"</strong> or <strong>"<EMAIL>"</strong> as username
                </p>
            </div>
        </div>

        <form method="POST" style="margin-bottom: 30px;">
            <!-- Epic Username Field -->
            <div style="margin-bottom: 25px;">
                <label for="username" style="display: block; color: white; font-weight: 700; margin-bottom: 10px; font-size: 16px;">
                    <i class="fas fa-user" style="margin-right: 8px; color: #ffd93d;"></i>
                    Username or Email
                </label>
                <input type="text" id="username" name="username"
                       style="width: 100%; padding: 18px 20px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); color: white; font-size: 16px; font-weight: 500; transition: all 0.3s ease;"
                       placeholder="<NAME_EMAIL>"
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                       required autofocus
                       onfocus="this.style.borderColor='rgba(255,215,61,0.8)'; this.style.background='rgba(255,255,255,0.15)'"
                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.1)'">
            </div>

            <!-- Epic Password Field -->
            <div style="margin-bottom: 25px;">
                <label for="password" style="display: block; color: white; font-weight: 700; margin-bottom: 10px; font-size: 16px;">
                    <i class="fas fa-lock" style="margin-right: 8px; color: #ffd93d;"></i>
                    Password
                </label>
                <input type="password" id="password" name="password"
                       style="width: 100%; padding: 18px 20px; border: 2px solid rgba(255,255,255,0.3); border-radius: 15px; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); color: white; font-size: 16px; font-weight: 500; transition: all 0.3s ease;"
                       placeholder="admin123"
                       required
                       onfocus="this.style.borderColor='rgba(255,215,61,0.8)'; this.style.background='rgba(255,255,255,0.15)'"
                       onblur="this.style.borderColor='rgba(255,255,255,0.3)'; this.style.background='rgba(255,255,255,0.1)'">
            </div>

            <!-- Error Message -->
            <?php if ($error): ?>
                <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 25px; text-align: center; border: 2px solid rgba(255,107,107,0.3); box-shadow: 0 10px 25px rgba(255,107,107,0.3);">
                    <i class="fas fa-exclamation-triangle" style="font-size: 20px; margin-bottom: 8px;"></i>
                    <div style="font-weight: 600; font-size: 16px;"><?php echo htmlspecialchars($error); ?></div>
                </div>
            <?php endif; ?>

            <!-- Epic Login Button -->
            <button type="submit"
                    style="width: 100%; padding: 20px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border: none; border-radius: 15px; font-size: 18px; font-weight: 700; cursor: pointer; transition: all 0.4s ease; display: flex; align-items: center; justify-content: center; gap: 12px; box-shadow: 0 15px 35px rgba(67,233,123,0.4); margin-bottom: 25px;"
                    onmouseover="this.style.transform='translateY(-3px) scale(1.02)'; this.style.boxShadow='0 25px 50px rgba(67,233,123,0.6)'"
                    onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 15px 35px rgba(67,233,123,0.4)'">
                <i class="fas fa-sign-in-alt"></i>
                Sign In to Admin Panel
            </button>
        </form>

        <!-- Back to Website Link -->
        <div style="text-align: center; padding-top: 25px; border-top: 1px solid rgba(255,255,255,0.2);">
            <a href="<?php echo SITE_URL; ?>"
               style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 600; font-size: 16px; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 10px;"
               onmouseover="this.style.color='white'; this.style.transform='translateX(-5px)'"
               onmouseout="this.style.color='rgba(255,255,255,0.8)'; this.style.transform='translateX(0)'">
                <i class="fas fa-arrow-left"></i> Back to Website
            </a>
        </div>

        <!-- Default Credentials Info -->
        <div style="background: rgba(255,255,255,0.05); backdrop-filter: blur(10px); border-radius: 15px; padding: 20px; margin-top: 25px; border: 1px solid rgba(255,255,255,0.1); text-align: center;">
            <h4 style="color: white; margin-bottom: 15px; font-size: 16px; font-weight: 700;">
                <i class="fas fa-key" style="color: #ffd93d; margin-right: 8px;"></i>
                Default Admin Credentials
            </h4>
            <div style="color: rgba(255,255,255,0.9); font-size: 14px; line-height: 1.6;">
                <div style="margin-bottom: 8px;"><strong>Username:</strong> admin</div>
                <div style="margin-bottom: 8px;"><strong>Email:</strong> <EMAIL></div>
                <div style="margin-bottom: 12px;"><strong>Password:</strong> admin123</div>
                <div style="font-size: 12px; color: rgba(255,255,255,0.7); font-style: italic;">
                    ⚠️ Change password after first login
                </div>
            </div>
        </div>
    </div>

    <!-- Shimmer Animation -->
    <style>
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Input placeholder styling */
        input::placeholder {
            color: rgba(255,255,255,0.6) !important;
            font-weight: 500;
        }

        /* Mobile responsive */
        @media (max-width: 480px) {
            body > div {
                padding: 30px 25px !important;
                margin: 20px !important;
            }

            h1 {
                font-size: 28px !important;
            }

            input, button {
                padding: 16px 18px !important;
                font-size: 15px !important;
            }
        }
    </style>
</body>
</html>
