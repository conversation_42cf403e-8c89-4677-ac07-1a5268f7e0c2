<?php
require_once '../config/config.php';

$pageTitle = 'Category Management';

// Check if user is admin
if (!is_admin()) {
    redirect('login.php');
}

$db = GeusGalore\Database::getInstance();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $name = sanitize_input($_POST['name']);
        $description = sanitize_input($_POST['description']);
        $slug = generate_slug($name);
        $parentId = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        if (!empty($name)) {
            // Check if slug exists
            $existingSlug = $db->fetch("SELECT id FROM categories WHERE slug = ?", [$slug]);
            if ($existingSlug) {
                $slug .= '-' . time();
            }
            
            $result = $db->insert('categories', [
                'name' => $name,
                'slug' => $slug,
                'description' => $description,
                'parent_id' => $parentId,
                'is_active' => $isActive
            ]);
            
            if ($result) {
                $message = 'Category added successfully!';
                $messageType = 'success';
            } else {
                $message = 'Failed to add category.';
                $messageType = 'error';
            }
        } else {
            $message = 'Category name is required.';
            $messageType = 'error';
        }
    }
    
    elseif ($action === 'edit' && isset($_POST['category_id'])) {
        $categoryId = intval($_POST['category_id']);
        $name = sanitize_input($_POST['name']);
        $description = sanitize_input($_POST['description']);
        $slug = generate_slug($name);
        $parentId = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        if (!empty($name)) {
            // Check if slug exists (excluding current category)
            $existingSlug = $db->fetch("SELECT id FROM categories WHERE slug = ? AND id != ?", [$slug, $categoryId]);
            if ($existingSlug) {
                $slug .= '-' . time();
            }
            
            $result = $db->update('categories', [
                'name' => $name,
                'slug' => $slug,
                'description' => $description,
                'parent_id' => $parentId,
                'is_active' => $isActive
            ], 'id = ?', [$categoryId]);
            
            if ($result) {
                $message = 'Category updated successfully!';
                $messageType = 'success';
            } else {
                $message = 'Failed to update category.';
                $messageType = 'error';
            }
        } else {
            $message = 'Category name is required.';
            $messageType = 'error';
        }
    }
    
    elseif ($action === 'delete' && isset($_POST['category_id'])) {
        $categoryId = intval($_POST['category_id']);
        
        // Check if category has products
        $productCount = $db->fetch("SELECT COUNT(*) as count FROM products WHERE category_id = ?", [$categoryId])['count'];
        
        if ($productCount > 0) {
            $message = 'Cannot delete category. It has ' . $productCount . ' products assigned to it.';
            $messageType = 'error';
        } else {
            // Check if category has subcategories
            $subcategoryCount = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE parent_id = ?", [$categoryId])['count'];
            
            if ($subcategoryCount > 0) {
                $message = 'Cannot delete category. It has ' . $subcategoryCount . ' subcategories.';
                $messageType = 'error';
            } else {
                $result = $db->query("DELETE FROM categories WHERE id = ?", [$categoryId]);
                
                if ($result) {
                    $message = 'Category deleted successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to delete category.';
                    $messageType = 'error';
                }
            }
        }
    }
}

// Get categories with product counts
$categories = $db->fetchAll("
    SELECT c.*, 
           COUNT(p.id) as product_count,
           parent.name as parent_name
    FROM categories c 
    LEFT JOIN products p ON c.id = p.category_id 
    LEFT JOIN categories parent ON c.parent_id = parent.id
    GROUP BY c.id 
    ORDER BY c.parent_id ASC, c.name ASC
");

// Get parent categories for dropdown
$parentCategories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY name");

include 'includes/admin-header.php';
?>

<div class="container" style="margin: 40px auto; padding: 0 20px;">
    <!-- Page Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; flex-wrap: wrap; gap: 20px;">
        <div>
            <h1 style="color: var(--charcoal); margin-bottom: 10px; font-size: 32px; font-weight: 800;">
                <i class="fas fa-tags"></i> Category Management
            </h1>
            <p style="color: var(--medium-brown); font-size: 16px;">Organize your products with categories</p>
        </div>
        <button onclick="showAddModal()" class="btn btn-primary" style="background: var(--gradient-sunset); border: none; padding: 15px 30px; font-size: 16px; font-weight: 700;">
            <i class="fas fa-plus"></i> Add New Category
        </button>
    </div>

    <?php if ($message): ?>
        <div style="background: <?php echo $messageType === 'success' ? 'var(--success)' : 'var(--danger)'; ?>; color: white; padding: 15px 20px; border-radius: 10px; margin-bottom: 30px;">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <!-- Categories Table -->
    <div class="data-table">
        <div style="padding: 20px; background: var(--gradient-ocean); color: white; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-weight: 700;">
                <i class="fas fa-list"></i> Categories (<?php echo count($categories); ?>)
            </h3>
        </div>
        
        <?php if (empty($categories)): ?>
            <div style="text-align: center; padding: 60px 20px; color: var(--medium-brown);">
                <i class="fas fa-tags" style="font-size: 64px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>No categories found</h3>
                <p>Create your first category to organize your products.</p>
                <button onclick="showAddModal()" class="btn btn-primary" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> Add Category
                </button>
            </div>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Parent</th>
                        <th>Products</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600; color: var(--charcoal); margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </div>
                                <div style="font-size: 12px; color: var(--medium-brown);">
                                    Slug: <?php echo htmlspecialchars($category['slug']); ?>
                                </div>
                                <?php if ($category['description']): ?>
                                    <div style="font-size: 12px; color: var(--medium-brown); margin-top: 5px;">
                                        <?php echo htmlspecialchars(substr($category['description'], 0, 100)) . (strlen($category['description']) > 100 ? '...' : ''); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($category['parent_name']): ?>
                                    <span style="background: var(--cream); color: var(--dark-brown); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                                        <?php echo htmlspecialchars($category['parent_name']); ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: var(--medium-brown); font-style: italic;">Root Category</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="font-weight: 600; color: var(--coral);">
                                    <?php echo number_format($category['product_count']); ?>
                                </span>
                                <?php if ($category['product_count'] > 0): ?>
                                    <a href="products.php?category=<?php echo $category['id']; ?>" 
                                       style="color: var(--coral); text-decoration: none; font-size: 12px; margin-left: 5px;">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $category['is_active'] ? 'active' : 'inactive'; ?>">
                                    <?php echo $category['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-size: 12px; color: var(--medium-brown);">
                                    <?php echo date('M j, Y', strtotime($category['created_at'])); ?>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)" 
                                            class="btn-icon" title="Edit Category">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>', <?php echo $category['product_count']; ?>)" 
                                            class="btn-icon btn-danger" title="Delete Category">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div id="categoryModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 10000; align-items: center; justify-content: center;">
    <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <h3 style="margin-bottom: 25px; color: var(--charcoal);" id="modalTitle">Add New Category</h3>
        
        <form method="POST" id="categoryForm">
            <input type="hidden" name="action" id="formAction" value="add">
            <input type="hidden" name="category_id" id="categoryId">
            
            <div class="form-group">
                <label class="form-label">Category Name *</label>
                <input type="text" name="name" id="categoryName" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">Description</label>
                <textarea name="description" id="categoryDescription" class="form-control" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label">Parent Category</label>
                <select name="parent_id" id="categoryParent" class="form-control">
                    <option value="">None (Root Category)</option>
                    <?php foreach ($parentCategories as $parent): ?>
                        <option value="<?php echo $parent['id']; ?>">
                            <?php echo htmlspecialchars($parent['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                    <input type="checkbox" name="is_active" id="categoryActive" checked>
                    <span>Active</span>
                </label>
            </div>
            
            <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                <button type="button" onclick="closeCategoryModal()" class="btn btn-outline">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Category
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 10000; align-items: center; justify-content: center;">
    <div style="background: white; padding: 30px; border-radius: 15px; max-width: 400px; width: 90%; text-align: center;">
        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--danger); margin-bottom: 20px;"></i>
        <h3 style="margin-bottom: 15px; color: var(--charcoal);">Delete Category</h3>
        <p style="margin-bottom: 25px; color: var(--medium-brown);" id="deleteMessage"></p>
        <div style="display: flex; gap: 15px; justify-content: center;">
            <button onclick="closeDeleteModal()" class="btn btn-outline">Cancel</button>
            <form method="POST" style="display: inline;" id="deleteForm">
                <input type="hidden" name="category_id" id="deleteCategoryId">
                <input type="hidden" name="action" value="delete">
                <button type="submit" class="btn" style="background: var(--danger); color: white;">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
function showAddModal() {
    document.getElementById('modalTitle').textContent = 'Add New Category';
    document.getElementById('formAction').value = 'add';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryActive').checked = true;
    document.getElementById('categoryModal').style.display = 'flex';
}

function editCategory(category) {
    document.getElementById('modalTitle').textContent = 'Edit Category';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('categoryId').value = category.id;
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categoryDescription').value = category.description || '';
    document.getElementById('categoryParent').value = category.parent_id || '';
    document.getElementById('categoryActive').checked = category.is_active == 1;
    document.getElementById('categoryModal').style.display = 'flex';
}

function closeCategoryModal() {
    document.getElementById('categoryModal').style.display = 'none';
}

function deleteCategory(categoryId, categoryName, productCount) {
    document.getElementById('deleteCategoryId').value = categoryId;
    
    let message = `Are you sure you want to delete "${categoryName}"?`;
    if (productCount > 0) {
        message = `Cannot delete "${categoryName}" because it has ${productCount} products assigned to it. Please move or delete the products first.`;
        document.getElementById('deleteForm').style.display = 'none';
    } else {
        message += ' This action cannot be undone.';
        document.getElementById('deleteForm').style.display = 'inline';
    }
    
    document.getElementById('deleteMessage').textContent = message;
    document.getElementById('deleteModal').style.display = 'flex';
}

function closeDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// Close modals on outside click
document.getElementById('categoryModal').addEventListener('click', function(e) {
    if (e.target === this) closeCategoryModal();
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) closeDeleteModal();
});
</script>

<?php include 'includes/admin-footer.php'; ?>
