<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 New Landing Page Preview - Geu's Galore</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-container {
            max-width: 800px;
            padding: 60px 40px;
            text-align: center;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }
        
        .preview-title {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #FFD93D, #FF8E53);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .preview-subtitle {
            font-size: 24px;
            margin-bottom: 40px;
            opacity: 0.9;
            font-weight: 600;
        }
        
        .preview-description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 50px;
            opacity: 0.85;
        }
        
        .preview-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .preview-feature {
            background: rgba(255,255,255,0.1);
            padding: 30px 20px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .preview-feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }
        
        .preview-feature-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .preview-feature-desc {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .preview-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .preview-btn {
            padding: 18px 36px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .preview-btn-primary {
            background: rgba(255,255,255,0.9);
            color: #667eea;
        }
        
        .preview-btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .preview-btn-secondary {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 2px solid rgba(255,255,255,0.5);
        }
        
        .preview-btn-secondary:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-3px) scale(1.05);
        }
        
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.05);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }
        
        .floating-element:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-30px); }
        }
        
        @media (max-width: 768px) {
            .preview-container {
                margin: 20px;
                padding: 40px 30px;
            }
            
            .preview-title {
                font-size: 36px;
            }
            
            .preview-subtitle {
                font-size: 20px;
            }
            
            .preview-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .preview-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element"></div>
        <div class="floating-element"></div>
        <div class="floating-element"></div>
    </div>
    
    <div class="preview-container">
        <h1 class="preview-title">🎉 Landing Page Redesigned!</h1>
        
        <p class="preview-subtitle">
            Your Geu's Galore homepage is now absolutely stunning!
        </p>
        
        <p class="preview-description">
            I've completely transformed your landing page with a jaw-dropping design that will captivate visitors 
            and showcase Papua New Guinea's vibrant culture. The new design features modern gradients, 
            smooth animations, and a mobile-first responsive approach.
        </p>
        
        <div class="preview-features">
            <div class="preview-feature">
                <div class="preview-feature-icon">🌈</div>
                <div class="preview-feature-title">Stunning Gradients</div>
                <div class="preview-feature-desc">Beautiful PNG-inspired color combinations</div>
            </div>
            
            <div class="preview-feature">
                <div class="preview-feature-icon">✨</div>
                <div class="preview-feature-title">Smooth Animations</div>
                <div class="preview-feature-desc">Floating elements and morphing shapes</div>
            </div>
            
            <div class="preview-feature">
                <div class="preview-feature-icon">📱</div>
                <div class="preview-feature-title">Mobile Perfect</div>
                <div class="preview-feature-desc">Responsive design for all devices</div>
            </div>
            
            <div class="preview-feature">
                <div class="preview-feature-icon">🚀</div>
                <div class="preview-feature-title">Modern UI</div>
                <div class="preview-feature-desc">Glass morphism and backdrop blur</div>
            </div>
        </div>
        
        <div style="margin-bottom: 40px;">
            <h3 style="margin-bottom: 20px; font-size: 24px;">✅ What's New:</h3>
            <div style="text-align: left; max-width: 500px; margin: 0 auto;">
                <p>🎨 <strong>Hero Section:</strong> Full-screen gradient with floating animations</p>
                <p>🛍️ <strong>Categories:</strong> Glass morphism cards with hover effects</p>
                <p>⭐ <strong>Features:</strong> Interactive cards with smooth transitions</p>
                <p>📧 <strong>Newsletter:</strong> Modern signup with backdrop blur</p>
                <p>📱 <strong>Mobile:</strong> Perfect responsive design</p>
            </div>
        </div>
        
        <div class="preview-buttons">
            <a href="index.php" class="preview-btn preview-btn-primary">
                🏠 View New Homepage
            </a>
            <a href="ui-showcase.php" class="preview-btn preview-btn-secondary">
                🎨 UI Showcase
            </a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.2);">
            <p style="font-size: 14px; opacity: 0.7;">
                🗑️ Delete this preview file after testing: <code>landing-preview.php</code>
            </p>
        </div>
    </div>
    
    <script>
        console.log('🎉 Landing page redesign complete!');
        console.log('✨ Features: Gradients, animations, glass morphism, responsive design');
        console.log('🚀 Ready to impress visitors!');
        
        // Add some interactive sparkle effect
        document.addEventListener('mousemove', function(e) {
            if (Math.random() > 0.9) {
                const sparkle = document.createElement('div');
                sparkle.style.position = 'fixed';
                sparkle.style.left = e.clientX + 'px';
                sparkle.style.top = e.clientY + 'px';
                sparkle.style.width = '4px';
                sparkle.style.height = '4px';
                sparkle.style.background = 'white';
                sparkle.style.borderRadius = '50%';
                sparkle.style.pointerEvents = 'none';
                sparkle.style.zIndex = '1000';
                sparkle.style.animation = 'sparkle 1s ease-out forwards';
                document.body.appendChild(sparkle);
                
                setTimeout(() => {
                    sparkle.remove();
                }, 1000);
            }
        });
        
        // Add sparkle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkle {
                0% { opacity: 1; transform: scale(0); }
                50% { opacity: 1; transform: scale(1); }
                100% { opacity: 0; transform: scale(0); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
