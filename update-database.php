<?php
require_once 'config/config.php';

echo "<h1>🔧 Database Schema Update Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f0f2f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #007bff; }
    .warning { color: #ffc107; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
    .btn:hover { background: #0056b3; }
</style>";

echo "<div class='container'>";

try {
    $db = GeusGalore\Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<h2>🔍 Checking Database Schema</h2>";
    
    // Check if we should apply updates
    $applyUpdates = isset($_GET['apply']) && $_GET['apply'] === 'yes';
    
    // List of required columns and their definitions
    $requiredUpdates = [
        [
            'table' => 'users',
            'column' => 'status',
            'definition' => "ENUM('active', 'inactive', 'suspended') DEFAULT 'active'",
            'description' => 'User account status'
        ],
        [
            'table' => 'users',
            'column' => 'loyalty_points',
            'definition' => "INT DEFAULT 0",
            'description' => 'User loyalty points'
        ],
        [
            'table' => 'quote_requests',
            'column' => 'user_id',
            'definition' => "INT NULL",
            'description' => 'Link to user account'
        ],
        [
            'table' => 'quote_requests',
            'column' => 'product_id',
            'definition' => "INT NULL",
            'description' => 'Link to specific product'
        ],
        [
            'table' => 'quote_requests',
            'column' => 'contact_name',
            'definition' => "VARCHAR(100) NOT NULL",
            'description' => 'Contact person name'
        ],
        [
            'table' => 'quote_requests',
            'column' => 'company_name',
            'definition' => "VARCHAR(100) NULL",
            'description' => 'Company name'
        ],
        [
            'table' => 'quote_requests',
            'column' => 'quantity',
            'definition' => "INT DEFAULT 1",
            'description' => 'Requested quantity'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_first_name',
            'definition' => "VARCHAR(50) NULL",
            'description' => 'Billing first name'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_last_name',
            'definition' => "VARCHAR(50) NULL",
            'description' => 'Billing last name'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_email',
            'definition' => "VARCHAR(100) NULL",
            'description' => 'Billing email'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_phone',
            'definition' => "VARCHAR(20) NULL",
            'description' => 'Billing phone'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_address',
            'definition' => "TEXT NULL",
            'description' => 'Billing address'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_city',
            'definition' => "VARCHAR(50) NULL",
            'description' => 'Billing city'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_state',
            'definition' => "VARCHAR(50) NULL",
            'description' => 'Billing state'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_postal_code',
            'definition' => "VARCHAR(20) NULL",
            'description' => 'Billing postal code'
        ],
        [
            'table' => 'orders',
            'column' => 'billing_country',
            'definition' => "VARCHAR(50) NULL",
            'description' => 'Billing country'
        ],
        [
            'table' => 'orders',
            'column' => 'subtotal',
            'definition' => "DECIMAL(10,2) DEFAULT 0",
            'description' => 'Order subtotal'
        ]
    ];
    
    $missingColumns = [];
    $existingTables = [];
    
    // Check each table and column
    foreach ($requiredUpdates as $update) {
        $tableName = $update['table'];
        
        // Check if table exists
        if (!in_array($tableName, $existingTables)) {
            try {
                $stmt = $pdo->query("DESCRIBE $tableName");
                $existingTables[] = $tableName;
                echo "<div class='success'>✅ Table '$tableName' exists</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ Table '$tableName' does not exist</div>";
                continue;
            }
        }
        
        // Check if column exists
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM $tableName LIKE '{$update['column']}'");
            $columnExists = $stmt->fetch();
            
            if (!$columnExists) {
                $missingColumns[] = $update;
                echo "<div class='warning'>⚠️ Missing: {$tableName}.{$update['column']} - {$update['description']}</div>";
            } else {
                echo "<div class='success'>✅ Exists: {$tableName}.{$update['column']}</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error checking {$tableName}.{$update['column']}: " . $e->getMessage() . "</div>";
        }
    }
    
    if (empty($missingColumns)) {
        echo "<h2>🎉 Database Schema is Up to Date!</h2>";
        echo "<div class='success'>✅ All required columns exist. Your admin dashboard should work now!</div>";
        echo "<p><a href='admin/index.php' class='btn'>🚀 Go to Admin Dashboard</a></p>";
    } else {
        echo "<h2>🔧 Missing Database Columns Found</h2>";
        echo "<p>Found " . count($missingColumns) . " missing columns that need to be added.</p>";
        
        if (!$applyUpdates) {
            echo "<div class='warning'>";
            echo "<strong>⚠️ Ready to Update Database</strong><br>";
            echo "The following columns will be added to your database:";
            echo "<ul>";
            foreach ($missingColumns as $update) {
                echo "<li><strong>{$update['table']}.{$update['column']}</strong> - {$update['description']}</li>";
            }
            echo "</ul>";
            echo "</div>";
            
            echo "<p><strong>Choose an option:</strong></p>";
            echo "<p><a href='?apply=yes' class='btn' style='background: #28a745;'>🔧 Apply Updates Automatically</a></p>";
            echo "<p><a href='#manual' class='btn' style='background: #ffc107; color: #000;'>📋 Show Manual SQL Commands</a></p>";
            
            echo "<h3 id='manual'>📋 Manual SQL Commands</h3>";
            echo "<p>If you prefer to run the updates manually in phpMyAdmin, use these commands:</p>";
            echo "<pre>";
            foreach ($missingColumns as $update) {
                echo "ALTER TABLE {$update['table']} ADD COLUMN {$update['column']} {$update['definition']};\n";
            }
            echo "</pre>";
            
        } else {
            echo "<h3>🔧 Applying Database Updates...</h3>";
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($missingColumns as $update) {
                $sql = "ALTER TABLE {$update['table']} ADD COLUMN {$update['column']} {$update['definition']}";
                echo "<div class='info'>Executing: <code>$sql</code></div>";
                
                try {
                    $pdo->exec($sql);
                    echo "<div class='success'>✅ Added {$update['table']}.{$update['column']} successfully</div>";
                    $successCount++;
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Failed to add {$update['table']}.{$update['column']}: " . $e->getMessage() . "</div>";
                    $errorCount++;
                }
            }
            
            echo "<h3>📊 Update Summary</h3>";
            echo "<div class='success'>✅ Successfully added: $successCount columns</div>";
            if ($errorCount > 0) {
                echo "<div class='error'>❌ Failed to add: $errorCount columns</div>";
            }
            
            if ($errorCount === 0) {
                echo "<h2>🎉 Database Update Complete!</h2>";
                echo "<div class='success'>✅ All required columns have been added successfully!</div>";
                echo "<p><a href='admin/index.php' class='btn'>🚀 Go to Admin Dashboard</a></p>";
            } else {
                echo "<h2>⚠️ Partial Update Complete</h2>";
                echo "<div class='warning'>Some columns couldn't be added automatically. Please add them manually using the SQL commands above.</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database Connection Error: " . $e->getMessage() . "</div>";
    echo "<h3>🔧 Manual Fix Instructions:</h3>";
    echo "<p>Since the automatic update failed, please run these SQL commands manually in phpMyAdmin:</p>";
    
    echo "<pre>";
    echo "-- Add missing columns to users table\n";
    echo "ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active';\n";
    echo "ALTER TABLE users ADD COLUMN loyalty_points INT DEFAULT 0;\n\n";
    
    echo "-- Add missing columns to quote_requests table\n";
    echo "ALTER TABLE quote_requests ADD COLUMN user_id INT NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN product_id INT NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN contact_name VARCHAR(100) NOT NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN company_name VARCHAR(100) NULL;\n";
    echo "ALTER TABLE quote_requests ADD COLUMN quantity INT DEFAULT 1;\n\n";
    
    echo "-- Add missing columns to orders table\n";
    echo "ALTER TABLE orders ADD COLUMN billing_first_name VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_last_name VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_email VARCHAR(100) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_phone VARCHAR(20) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_address TEXT NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_city VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_state VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_postal_code VARCHAR(20) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN billing_country VARCHAR(50) NULL;\n";
    echo "ALTER TABLE orders ADD COLUMN subtotal DECIMAL(10,2) DEFAULT 0;\n";
    echo "</pre>";
}

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>";
echo "⚠️ <strong>Security Note:</strong> Delete this file (update-database.php) after running it!";
echo "</p>";

echo "</div>";
?>
